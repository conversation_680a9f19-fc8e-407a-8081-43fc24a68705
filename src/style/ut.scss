@use "sass:math";

// 默认设计稿的宽度
$designWidth: 1920;
// 默认设计稿的高度
$designHeight: 1080;
$aspect-ratio: math.div(16, 9);

// px 转为 vw 的函数
@function vw($px) {
  $current-ratio: var(--current-ratio);

  @if $current-ratio < $aspect-ratio {
    @return math.div($px, $designWidth) * 100vw;
  } @else {
    $rightWidth: math.div(100vh, $designHeight) * $designWidth;
    @return math.div($px, $designWidth) * $rightWidth;
  }
}

// px 转为 vh 的函数
@function vh($px) {
  @return math.div($px, $designHeight) * 100vh;
}