<template>
  <div>
    <p class="title">{{ title }}</p>
    <div class="numbers">
      <div v-for="(item, i) in letters" :key="i" class="letter">{{ item }}</div>
    </div>
  </div>
</template>

<script setup name="TotalNum" lang="ts">
import { ref, watchEffect } from 'vue'
const props = defineProps({
  title: {
    type: String,
    default: 'title',
  },
  total: {
    type: Number,
    default: 0,
  }
})
const letters = ref<string[]>([])

watchEffect(() => {
  letters.value = props.total.toString().split('')
  while (letters.value.length < 4) {
    letters.value.unshift('0')
  }
})

</script>

<style scoped lang="scss">
.title {
  font-family: Source Han Sans;
  font-size: vh(18);
  font-weight: bold;
  line-height: normal;
  letter-spacing: 0px;
  color: #18CACA;

  text-align: center;
}


.numbers {
  display: flex;
  gap: 4px;
  margin-top: vh(9);
}

.letter {

  width: vw(40);
  height: vh(50);

  background-image: url("../assets/images/totalNumber.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;

  font-family: YouSheBiaoTiHei;
  font-size: vh(36);
  font-weight: normal;
  line-height: vh(40);
  text-align: center;
  letter-spacing: 0px;
  color: #18CACA;

  // 文字在div中水平垂直居中
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>