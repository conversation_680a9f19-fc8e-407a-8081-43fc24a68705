<template>
  <div class="center-container">
    <div class="mapWrapper">
      <div class="centerBorder">
        <img src="@/assets/images/mapBorderLeft.svg" class="mapBorder">
        <slot name="mapContainer"></slot>
        <img src="@/assets/images/mapBorderRight.svg" class="mapBorder">
      </div>
      <div class="total">
        <slot name="top"></slot>
      </div>
      <div class="map">
        <slot name="map"></slot>
      </div>
    </div>
    <div class="bottom" ref="bottomRef">
      <large-border :title="props.title">
        <template #default>
          <slot name="bottom"></slot>
        </template>
      </large-border>
    </div>
  </div>
</template>

<script setup lang="ts" name="center">
import LargeBorder from "@/components/border/LargeBorder.vue";
import { ref } from 'vue'
const props = withDefaults(
  defineProps<{ title?: string | string[] }>(),
  {
    title: '机构统计',
  })

const bottomRef = ref()

// const isFullScreen = () => {
//   return document.fullscreenElement !== null;
// };

// onBeforeUpdate(() => {
//   if (isFullScreen()) {
//     bottomRef.value.style.top = -13.5 / 1080 + 'vh'
//   }
//   else {
//     bottomRef.value.style.top = -(13.5 + 20) / 1080 + 'vh'
//   }
// })

// const handleFullscreenChange = () => {
//   console.log('isFullScreen!!!', isFullScreen());
//   if (isFullScreen()) {
//     bottomRef.value.style.top = 0 + 'vh'
//   }
//   else {
//     bottomRef.value.style.top = -(13.5 + 20) / 1080 * 100 + 'vh'
//   }
// }

// onMounted(() => {
//   window.addEventListener('fullscreenchange', handleFullscreenChange);
// })

// onBeforeUnmount(() => {
//   window.removeEventListener('fullscreenchange', handleFullscreenChange);
// })

</script>

<style scoped lang="scss">
.center-container {
  width: 100%;
  height: calc(100% + vh(20));

  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;

  top: vh(-13.5);

  .mapWrapper {
    height: calc(100% / 3 * 2);
    display: flex;
    flex-direction: column;
    align-items: center;

    .centerBorder {
      width: 100%;
      height: calc(100vh - vh(325) - vw(126));
      position: absolute;
      top: vh(13.5);

      display: flex;
      align-items: center;
      justify-content: space-between;

      .mapBorder {
        width: vw(157.4);
        height: vh(705.5);
        position: relative;
      }
    }

    .total {
      width: vw(950);
      height: vh(84);
      display: flex;
      justify-content: center;
      gap: vw(100);

      position: relative;
      top: vh(13.5);

    }

    .map {
      width: 100%;
      // height: calc(100vh - vh(325) - vw(126));
      // height: vh(325 * 2 - 84);
      height: calc(100vh - vh(325) - vw(126) - vh(84));

      // flex: 1;

      position: relative;
      top: vh(13.5);
    }
  }

  .bottom {
    width: vw(950);
    // height: vh(325);
    height: calc(100% / 3 * 1);

    position: relative;
    // top: vh(-13.5 ) // TODO 全屏状态不-20
  }
}
</style>
