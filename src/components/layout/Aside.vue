<template>
  <div class="aside">
    <!-- <div class="aside-item" > -->
    <NormalBorder :title="topTitle" class="aside-item">
      <slot name="top"></slot>
    </NormalBorder>
    <!-- </div> -->
    <!-- <div class="aside-item"> -->
    <NormalBorder :title="centerTitle" class="aside-item">
      <slot name="center"></slot>
    </NormalBorder>
    <!-- </div> -->
    <!-- <div class="aside-item"> -->
    <NormalBorder :title="bottomTitle" class="aside-item">
      <slot name="bottom"></slot>
    </NormalBorder>
    <!-- </div> -->
  </div>
</template>

<script setup lang="ts" name="aside">
import NormalBorder from "../border/NormalBorder.vue";

defineProps({
  topTitle: {
    type: String,
    default: "Title"
  },
  centerTitle: {
    type: String,
    default: "Title"
  },
  bottomTitle: {
    type: String,
    default: "Title"
  },
  total: Number
})

</script>

<style scoped lang="scss">
.aside {
  width: 100%;
  height: calc(100% + vh(20));
  display: flex;
  flex-direction: column;
  // flex-wrap: wrap; 
  align-items: center;
  justify-content: space-between;
  position: relative;
  top: vh(-13.5);

  .aside-item {
    flex: 1;
    // width: vw(449);
    // height: vh(325);
  }
}
</style>