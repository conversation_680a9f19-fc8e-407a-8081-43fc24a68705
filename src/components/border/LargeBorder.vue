<template>
    <div class="largeBorder ">
        <div ref="lottieBorderRef" class="lottieBorder"></div>
        <div class="sub-box" :style="{ width: resizeWidth, height: resizeHeight }">
            <div class="sub-box-top">
                <div v-if="Object.prototype.toString.call(props.title) === '[object Array]'" class="titleArr">
                    <div style="flex:1" v-for="(title, i) in props.title" :key="i">{{ title }}</div>
                </div>
                <div v-else class="sub-box-title">{{ props.title }}</div>
                <div class="sub-box-line"></div>
            </div>
            <div class="sub-box-slot">
                <slot></slot>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts" name="LargeBorder">
import lottie from 'lottie-web';
import animationData from '@/assets/lottie/largeBorder.json'; // 路径需要根据实际情况修改
import { ref, onMounted } from "vue";
import { emitter } from "@/utils/mitt";
// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { offsetWidth, offsetHeight, standardWidth } = storeToRefs(dashBoardStore)
// props
const props = defineProps<{ title: string | string[] }>()

const lottieBorderRef = ref()
const initLottie = () => {
    lottie.loadAnimation({
        container: lottieBorderRef.value, // 在模板中添加一个 ref="lottieContainer" 的容器
        animationData: animationData,
        renderer: 'svg', // 选择渲染器，可以是 'svg'、'canvas'、'html'
        loop: true, // 是否循环播放
        autoplay: true, // 是否自动播放
    });
}

// box自适应高度
const resizeWidth = ref('860px')
const resizeHeight = ref('321px')

const resizeFun = () => {
    // console.log('offsetWidth', offsetWidth.value);
    // console.log('offsetHeight', offsetHeight.value);
    if (offsetWidth.value / offsetHeight.value >= 16 / 9) {
        resizeWidth.value = (offsetHeight.value / 1080 * 950) + 'px'
        resizeHeight.value = (offsetHeight.value / 1080 * 321) + 'px'
    } else {
        resizeWidth.value = (offsetWidth.value / standardWidth.value * 950) + 'px'
        resizeHeight.value = (offsetWidth.value / standardWidth.value * 321) + 'px'
    }

}

onMounted(() => {
    resizeFun()
    initLottie()
    emitter.on('resize', resizeFun)
})

</script>

<style scoped lang="scss">
.largeBorder {
    width: 100%;
    height: 100%;
    position: relative;

    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .sub-box {
        position: relative;
        // width: vw(860);
        width: 100%;
        height: 100%;
        padding: vh(37) 3.5%;
        display: flex;
        flex-direction: column;
        align-items: center;

        .sub-box-top {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .sub-box-title {
                width: 100%;

                font-size: vw(18);
                font-family: Source Han Sans CN, Source Han Sans CN-Medium;
                font-weight: Medium;
                text-align: left;
                color: #44FFFF;
                text-shadow: 0px 0px 8px rgba(86, 254, 254, 0.53);
            }

            .sub-box-line {
                width: 100%;

                height: 3px;
                background-image: url("../../assets/images/sub-box-line2.svg");
                background-size: 100% auto;
            }
        }

        .sub-box-slot {
            width: 100%;
            // flex: 1;
            height: calc(100% - 3px - vw(18));
        }
    }

}

.titleArr {
    width: 100%;
    display: flex;

    font-size: vw(18);
    font-weight: Medium;
    color: #44FFFF;
    text-shadow: 0px 0px 8px rgba(86, 254, 254, 0.53);

    div {
        text-align: center;
    }
}

.lottieBorder {
    width: 100%;
    height: 100%;
    pointer-events: none;
    position: absolute;
}
</style>