<template>
    <div class="normal-border">
        <div ref="lottieBorderRef" class="lottieBorder"></div>
        <div class="sub-box" :style="{ width: resizeWidth, height: resizeHeight }">
            <div class="sub-box-top">
                <div class="sub-box-title">{{ props.title }}</div>
                <div class="sub-box-line"></div>
            </div>
            <div class="sub-box-slot">
                <slot name="default"></slot>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts" name="DashBoardBorder">
import lottie from 'lottie-web';
import animationData from '@/assets/lottie/normalBorder.json'; // 路径需要根据实际情况修改
import { ref, onMounted, onBeforeUnmount } from "vue";
import { emitter } from "@/utils/mitt";
// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { offsetWidth, offsetHeight, standardWidth } = storeToRefs(dashBoardStore)

// props
const props = defineProps<{ title: string }>()

// lottie
const lottieBorderRef = ref()
const initLottie = () => {
    lottie.loadAnimation({
        container: lottieBorderRef.value, // 在模板中添加一个 ref="lottieContainer" 的容器
        animationData: animationData,
        renderer: 'svg', // 选择渲染器，可以是 'svg'、'canvas'、'html'
        loop: true, // 是否循环播放
        autoplay: true, // 是否自动播放
    });
}

// box自适应
const resizeWidth = ref('395px')
const resizeHeight = ref('234px')

// const boxWidth = ref(`${449 * 0.8}px`)
// const boxHeight = ref(`${325 * 0.8}px`)

const resizeFun = () => {
    // lottiebox的宽高
    // const lottieWidth = (document.getElementsByClassName('normal-border')[0] as HTMLElement).offsetHeight
    // const lottieHeight = (document.getElementsByClassName('normal-border')[0] as HTMLElement).offsetHeight;
    // console.log('lottiebox🍊', lottieWidth, lottieHeight);

    // if (lottieWidth / lottieHeight >= 449 / 325) { // 标准宽高 449*325（1920*1080尺寸）
    //     // console.log('computedBox🍊', lottieHeight / 325 * 449, lottieHeight);
    //     boxWidth.value = lottieHeight / 325 * 449 + 'px'
    //     boxHeight.value = lottieHeight * 0.8 + 'px'
    // } else {
    //     boxWidth.value = lottieWidth + 'px'
    //     boxHeight.value = lottieWidth / 449 * 325 + 'px'
    // }

    if (offsetWidth.value / offsetHeight.value >= 16 / 9) {
        resizeWidth.value = (offsetHeight.value / 1080 * 395 ) + 'px'
        resizeHeight.value = (offsetHeight.value / 1080 * 234 ) + 'px'
    } else {
        resizeWidth.value = (offsetWidth.value / standardWidth.value * 395) + 'px'
        resizeHeight.value = (offsetWidth.value / standardWidth.value * 234) + 'px'
    }

}

onMounted(() => {
    resizeFun()
    initLottie()
    emitter.on('resize', resizeFun)
})

onBeforeUnmount(() => {
    emitter.off('resize')
})

</script>

<style scoped lang="scss">
.normal-border {
    width: 100%;
    height: 100%;
    position: relative;
    // box-sizing: border-box;
    // padding: vh(40) vw(27);

    display: flex;
    justify-content: center;
    align-items: center;

    .lottieBorder {
        width: 100%;
        height: 100%;

        pointer-events: none;
        position: absolute;
        top: 0;
        left: 0;
    }

    .sub-box {
        position: relative;
        width: 100%;
        height: 100%;

        display: flex;
        flex-direction: column;
        justify-content: center;

        // 向上偏移5%
        transform: translateY(-2.5%);

        .sub-box-top {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .sub-box-title {
                width: 100%;
                font-size: vh(18);
                height: 1.5em;
                line-height: 1.5em;
                font-family: Source Han Sans CN, Source Han Sans CN-Medium;
                font-weight: Medium;
                text-align: left;
                color: #44FFFF;
                font-weight: 500;
                text-shadow: 0px 0px 9px rgba(86, 254, 254, 0.53);

                position: relative;

                .titleSlot {
                    position: absolute;
                    right: 0;
                }
            }

            .sub-box-line {
                width: 100%;
                height: 3px;
                background-image: url("@/assets/images/sub-box-line.svg");
                background-size: 100% auto;
                background-repeat: no-repeat;
            }
        }



        .sub-box-slot {
            width: 100%;
            height: calc(100% - vh(18 * 1.5) - 4px);
            // flex: 1;
        }

    }
}
</style>