<template>
    <div class="mapDetail-container" v-show="1">
        <div class="close"></div>
        <div class="title">
            <span style="font-size: 0.55vw;color: #18CACA;">区域：{{ props.title }}</span>
        </div>
        <div class="content">
            <div class="item" v-for="item in props.list" :key="item.name">
                <div class="item-left">
                    <span>
                        {{ item.name }}：
                    </span>
                </div>
                <div class="item-right">
                    <el-progress :percentage="item.percentage" color="#18CACA" style="width: 100%;">
                        <span class="value">{{ item.value }}</span>
                    </el-progress>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts" name="MapDetail">
const props = withDefaults(defineProps<{ title?: string, list?: any }>(), {
    title: 'public place',
    list: []
});


</script>

<style scoped lang="scss">
.mapDetail-container {

    width: 212.42px;
    height: 125px;
    padding: 17px 14px 20px 11px;
    background: url(../assets/images/mapDetail.svg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    // backdrop-filter: blur(6px);
    display: flex;
    flex-direction: column;
    position: relative;

    .close {
        position: absolute;
        width: 15px;
        height: 15.82px;
        top: 8px;
        right: 11px;
        background-image: url(../assets/images/close.svg);
        background-repeat: no-repeat;
        background-size: 100% 100%;
    }


    .title {
        width: 100%;
        height: 20%;
        font-size: 12px;
        display: flex;
        // justify-content: center;
    }

    .content {
        width: 100%;
        // height: 80%;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-evenly;

        .item {
            flex: 1;
            display: flex;
            height: 20%;
            font-size: 10px;
            justify-content: space-around;
        }

        .item-left {
            width: 32%;
            position: relative;
            // top: -25%;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            font-size: 8px;
        }

        .item-right {
            width: 68%;
            height: 100%;
            display: flex;
            align-items: center;

            .value {
                // vertical-align: center;
                font-size: 9.49px;
                // margin-left: 4px;
                color: #18CACA;
            }
        }
    }

    :deep(.el-progress-bar__outer) {
        background-color: transparent;
        border-radius: 0;
    }

    :deep(.el-progress-bar__inner) {
        border-radius: 0;
    }


}
</style>