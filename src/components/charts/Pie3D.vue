<template>
  <div class="wrap">
    <div ref="pieChartRef" class="pieChart"></div>
  </div>
</template>
<script setup lang="ts" name="NumberAndPie">
import * as echarts from 'echarts';
import type { EChartsType } from 'echarts';
import 'echarts-gl';
import { ref, onMounted, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'
import _ from 'lodash'

// pinia
import { storeToRefs } from 'pinia'
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { offsetHeight } = storeToRefs(dashBoardStore)

const props = withDefaults(defineProps<{ title?: string, seriesData?: any; tooltip?: any; label?: any }>(), {
  title: '单位类别数据图',
  seriesData: [
    { value: 48, name: '企业' },
    { value: 77, name: '其他医疗机构' },
    { value: 10, name: '专科医院' },
    { value: 33, name: '其他单位' },
    { value: 94, name: '综合性医院' },
  ],
  tooltip: () => ({}),
  label: () => ({}),
});

const color = [ // 颜色顺序是反的，color[0]对应的是seriesData最小的数据
  "rgba(250, 200, 88, 1)",
  "rgba(147, 190, 255, 1)",
  "rgba(80, 122, 252, 1)",
  "rgba(3, 127, 152, 1)",
  "#7bd9a5",
  "rgba(145, 204, 117, 1)",
  "rgba(104, 164, 0, 1)",
  "rgba(3, 51, 59, 1)",
  "rgba(40, 62, 129, 1)",
  "rgba(23, 227, 227, 1)",
]

const pieChartRef = ref(null)
const chartInstance = ref<EChartsType>();

// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
function getParametricEquation(startRatio: number, endRatio: number, isSelected: boolean, isHovered: any, k: number, h: number) {
  // 计算
  const midRatio = (startRatio + endRatio) / 2;
  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;
  const midRadian = midRatio * Math.PI * 2;
  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }
  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = 1;
  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  const hoverRate = isHovered ? 1.05 : 1;
  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },
    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },
    x: function (u: number, v: number) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    y: function (u: number, v: number) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },
    z: function (u: number, v: number) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1;
      }
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
    },
  };
}

/**
* 绘制3d图
* @param pieData 总数据
* @param internalDiameterRatio:透明的空心占比
*/
const getPie3D = (pieData: string | any[], internalDiameterRatio: number) => {
  const series = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  const k =
    typeof internalDiameterRatio !== 'undefined'
      ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3;
  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i += 1) {
    sumValue += +pieData[i].value;
    const seriesItem = {
      name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k,
      },
    };
    series.push(seriesItem);
  }
  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i += 1) {
    endValue = startValue + series[i].pieData.value;
    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].itemStyle = {
      color: color[i],
    };
    // @ts-ignore
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      Math.sqrt(series[i].pieData.value) * 10 // 用于控制高度
    );
    startValue = endValue;
  }
  return series;
}

const getOption = (optionsData: any) => {
  const series = getPie3D(optionsData, 0.6);
  series.push({
    name: 'pie2d',
    type: 'pie',
    // @ts-ignore
    startAngle: -120, //起始角度，支持范围[0, 360]。
    clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
    radius: ['20%', '55%'],
    center: ['50%', '50%'],
    data: optionsData,
    itemStyle: {
      opacity: 0,
    },
    label: _.merge({
      show: true,
      position: 'outside',
      rich: { // 富文本样式
        value: {
          fontFamily: 'DS-digital',
          fontSize: 16,
        }
      },
      formatter: '{b}\n{value|{d}%}',
      color: '#fff',
      opacity: 1,
    }, props.label),
    labelLine: {
      show: true,
    },
  });

  const option = {
    color,
    tooltip: {
      trigger: 'item',
      className: 'custom-tooltip-box',
      // 清除默认样式
      padding: 0,
      borderWidth: 0,
      borderColor: "transparent",
      backgroundColor: "transparent",
      textStyle: {
        color: "#FFFFFF",
        fontSize: 12
      },
      formatter: (params: { seriesName: string; seriesIndex: number; color: string; }) => {
        console.log('params', params)
        console.log('!!!series!!!', series)
        if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
          let bfb = ((series[params.seriesIndex].pieData.endRatio - series[params.seriesIndex].pieData.startRatio) * 100).toFixed(2);
          return (
            `${params.seriesName}<br/>` +
            `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
            `${bfb}%`
          );
        }
      },
    },
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 30,
      top: '10%',
      viewControl: { // 三维笛卡尔坐标系在三维场景中的高度
        alpha: 33,
        beta: 120,
        distance: 700,//调整视角到主体的距离，类似调整zoom
        rotateSensitivity: 0,// 设置为0无法旋转
        zoomSensitivity: 0,// 设置为0无法缩放
        panSensitivity: 0,// 设置为0无法平移
        autoRotate: false,// 自动旋转
      },
      postEffect: { // 后处理特效，让整个画面更富有质感
        enable: true, // 缺点是开启这项配置会出现锯齿
        bloom: { // 高光
          enable: true,
          bloomIntensity: 0,
        },
        SSAO: {
          enable: false,
          quality: 'medium',
          radius: 2,
        },
      },
    },
    series: series,
  };
  return option;
};
let option = {};

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

//初始化
const init = () => {
  if (!pieChartRef.value) return;

  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(pieChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(pieChartRef.value, undefined, { renderer: "canvas" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    const data = JSON.parse(JSON.stringify(props.seriesData)); // 深拷贝 注意！ data数组元素的value必须为number类型
    const total = props.seriesData.reduce((prev: number, cur: { value: number; }) => prev + cur.value, 0);
    if (total === 0) { // 如果没数据
      data.forEach((item: { value: number }) => {
        item.value = 40;
      });
      option = getOption(data.map((item: { value: number }) => {
        return { ...item, value: 40 };
      }));
      option.tooltip = _.merge(option.tooltip, { show: false })
      option.series[option.series.length - 1].label.formatter = '{b}\n{value|0%}';
    }
    else {
      data.sort((a: { value: number }, b: { value: number }) => a.value - b.value)
      option = getOption(data);
      option.tooltip = _.merge(option.tooltip, props.tooltip)
    }
    draw()
  },
  { immediate: true, deep: true }
)

const resizeChartfun = debounce(() => {
  // @ts-ignore
  option.series && (option.series[option.series.length - 1].label.fontSize = offsetHeight.value / 1080 * 12);
  // @ts-ignore
  option.series && (option.series[option.series.length - 1].label.rich.value.fontSize = offsetHeight.value / 1080 * 16);
  chartInstance.value && chartInstance.value.resize();
  draw()
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

</script>

<style scoped lang="scss">
.wrap {
  width: 100%;
  height: 100%;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  .pieChart {
    width: 100%;
    height: 100%;
  }
}

.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);
  box-shadow: none;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: transparent !important;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>