<template>
  <div class="rank-container">
    <div ref="barChartRef" class="barChart"></div>
  </div>
</template>
<script setup lang="ts" name="RegionalRank">
import * as echarts from 'echarts';
import { ref, onMounted, onBeforeUnmount, watch, markRaw, nextTick } from "vue";
import { emitter } from "@/utils/mitt";
import { debounce } from 'lodash';
import { getUnitShrink } from '@/utils/tools'

const props = withDefaults(defineProps<{ title?: string, axisData?: any, seriesData?: any, colorIndex?: number, yAxisName?: string }>(),
  {
    title: 'BarChart',
    // axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
    // seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    colorIndex: 0,
    yAxisName: ''
  })

const unit = ref(1)
const color = ['#19CCCC', '#FFD33C', '#00B5F1', '#8A7EEE', '#17BC84']

const option = {
  color: color[props.colorIndex],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    className: 'custom-tooltip-box',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 12
    },
    // 
    // formatter: function (params) {
    //   console.log(params)
    // },
  },
  grid: {
    top: '20%',
    left: '3%',
    right: '4%',
    bottom: '-0',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [],
    axisLabel: {
      color: '#D9D9D9',
      fontSize: 10,
      interval: 0,
    },
  },
  yAxis: {
    name: '',
    type: 'value',
    boundaryGap: [0, 0.01],
    max: (value: { min: number, max: number; }) => {  // 数据值很小时，设置最大值为10
      if (value.max <= 10) {
        return 10;
      }
      else return undefined;
    },
    // 分隔线
    splitLine: {
      lineStyle: {
        type: 'dashed',
        // color: '#D9D9D9',
        color: '#333'
      },
    },
    // 标签文字颜色
    axisLabel: {
      color: '#D9D9D9',
      fontSize: 10,
      formatter(value: number) {
        if (value) {
          // 返回100以内的整数
          return Math.floor(value / unit.value)
        }
      },
    },
  },

  series: [
    {
      name: props.title,
      type: 'bar',
      data: [],
      barWidth: '40%',
      barMaxWidth: '40%',
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 6
      },
    },
  ]
}

const barChartRef = ref()
const chartInstance = ref();

// 绘制(更新)
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// 初始化
const initBar = () => {
  if (!barChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(barChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(barChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
}

// resize
const resizeChartfun = debounce(() => {
  nextTick(() => {
    // option.xAxis.axisLabel.fontSize = (document.getElementsByClassName('layout-container')[0] as HTMLElement).offsetHeight / 1080 * 10
    chartInstance.value.resize();
    draw()
  });
}, 500)

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option.xAxis.data = props.axisData
    const data = JSON.parse(JSON.stringify(props.seriesData))
    option.series[0].data = data

    const res = getUnitShrink(data)
    option.yAxis.name = res.unitName + props.yAxisName
    unit.value = res.unit

    draw()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  initBar()
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
})

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.rank-container {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  @media screen and (max-width: 1960px) {
    font-size: 10px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 8px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 6px;
  }

  .barChart {
    width: 100%;
    height: 100%;
  }
}

</style>
