<template>
  <div class="rank-container">
    <div ref="barChartRef" class="barChart"></div>
  </div>
</template>
<script setup lang="ts" name="RegionalRank">
import * as echarts from 'echarts';
import { ref, onMounted, onBeforeUnmount, watch, markRaw, nextTick } from "vue";
import { emitter } from "@/utils/mitt";
import { debounce } from 'lodash';

const props = withDefaults(defineProps<{
  title?: string,
  axisData?: any, // x轴数据
  seriesData?: any, // y轴数据
  showYAxisSplitLine?: boolean,  // 是否显示y轴分隔线
  showSymbol?: boolean, // 是否显示symbol标记点(以及label数字)
  colorIndex?: number, // 颜色索引
  xAxisRotate?: boolean, // x轴旋转
}>(),
  {
    title: 'LineChart',
    axisData: ['2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024'],
    seriesData: [17, 39, 69, 56, 42, 81, 121, 144,],
    showYAxisSplitLine: true,
    showSymbol: false,
    colorIndex: 0,
    xAxisRotate: false,
  })

const unit = ref(1)
// default,yellow,bule,purple,green
const color = ['#19CCCC', '#FFD33C', '#00B5F1', '#8A7EEE', '#17BC84']
const areaColor = ['rgba(25, 204, 204, 0.37)', 'rgba(239, 199, 58, 0.6727)', 'rgba(0, 181, 241, 0.39)', ' rgba(138, 126, 238, 0.39)', 'rgba(23, 188, 132, 0.39)']

const getOption = () => {
  return {
    color: color[props.colorIndex],
    tooltip: {
      trigger: 'axis',
      className: 'custom-tooltip-box',
      // 清除默认样式
      padding: 0,
      borderWidth: 0,
      borderColor: "transparent",
      backgroundColor: "transparent",
      textStyle: {
        color: "#FFFFFF",
        fontSize: 12
      },
      // 
      // formatter: function (params) {
      //   console.log(params)
      // },
    },
    grid: {
      top: '20%',
      left: '3%',
      right: '4%',
      bottom: '-0',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: '#D9D9D9',
        fontSize: 8,
        interval: 0,
        rotate: props.xAxisRotate ? 30 : 0,
        textShadowOffsetX: props.xAxisRotate ? 12 : 0,
      },
    },
    yAxis: {
      show: true,
      name: '',
      type: 'value',
      boundaryGap: [0, 0.01],
      max: (value: { min: number, max: number; }) => {  // 百位起最大值向上取整
        if (value.max <= 10) {
          return 10;
        }
        else return undefined;
      },
      // 分隔线
      splitLine: {
        show: props.showYAxisSplitLine,
        lineStyle: {
          type: 'dashed',
          color: '#D9D9D9',
        },
      },
      // 标签文字颜色
      axisLabel: {
        color: '#D9D9D9',
        fontSize: 10,
        formatter(value: number) {
          if (value) {
            // 返回100以内的整数
            return Math.floor(value / unit.value)
          }
        },
      },
    },
    series: [
      {
        name: props.title,
        type: 'line',
        smooth: true,
        data: [],
        symbolSize: 10,
        lineStyle: { width: 4 },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 6,
        },
        areaStyle: { //区域填充样式
          //线性渐变，前4个参数分别是x0,y0,x2,y2(范围0~1);相当于图形包围盒中的百分比。如果最后一个参数是‘true’，则该四个值是绝对像素位置。
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: areaColor[props.colorIndex]
          },
          {
            offset: 1,
            color: "rgba(138, 126, 238, 0)"
          }
          ], false),
        },
      },
    ]
  }
}
let option = getOption()

const barChartRef = ref()
const chartInstance = ref();

// 绘制(更新)
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// 初始化
const initBar = () => {
  if (!barChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(barChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(barChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
}

// resize
const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value.resize();
    draw()
  });
}, 500)

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option = getOption()
    // 
    option.xAxis.data = props.axisData
    option.series[0].data = props.seriesData
    // 
    option.series[0].symbolSize = props.showSymbol ? 10 : 0
    option.series[0].label.show = props.showSymbol
    // 
    const max = Math.max(...props.seriesData)
    const hundred = 100 * 10 // 百
    const thousand = 1000 * 10 // 千
    const tenThousand = 10000 * 10 // 万
    const hundredThousand = 100000 * 10 // 十万
    const million = 1000000 * 10 // 百万
    const tenMillion = 10000000 * 10 // 千万
    if (max > tenMillion) {
      option.yAxis.name = '千万'
      unit.value = 10000000
    } else if (max > million) {
      option.yAxis.name = '百万'
      unit.value = 1000000
    } else if (max > hundredThousand) {
      option.yAxis.name = '十万'
      unit.value = 100000
    } else if (max > tenThousand) {
      option.yAxis.name = '万'
      unit.value = 10000
    } else if (max > thousand) {
      option.yAxis.name = '千'
      unit.value = 1000
    } else if (max > hundred) {
      option.yAxis.name = '百'
      unit.value = 100
    } else {
      option.yAxis.name = ''
      unit.value = 1
    }
    // option.yAxis.name = 

    draw()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  initBar()
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
})

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});
</script>

<style scoped lang="scss">
.rank-container {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  @media screen and (max-width: 1960px) {
    font-size: 10px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 8px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 6px;
  }

  .barChart {
    width: 100%;
    height: 100%;
  }
}

.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>
