<template>
    <div class="OrgStatistic-outerContainer">
        <div class="h100 w100 OrgStatistic">
            <div v-for="(item, i) in seriesData" :key="i" class="OrgStatistic-item">
                <div class="OrgStatistic-card">
                    <div class="number">
                        <div v-for="(letter, i) in item.value" :key="i" class="letter">{{ letter }}</div>
                    </div>
                    <p class="title">{{ item.title }}</p>
                </div>
            </div>
        </div>
    </div>
</template>


<script setup lang="ts" name="OrgStatistic">
import { ref, watchEffect } from "vue";
const props = withDefaults(defineProps<{ seriesData?: any; }>(), {
    seriesData: [
        { name: '职业卫生技术服务机构数', value: 0 },
        { name: '职业健康检查机构数', value: 0 },
        { name: '职业病鉴定机构数', value: 0 },
        { name: '放射卫生技术服务机构数', value: 0 },
        { name: '职业病诊断机构数', value: 0 },
        { name: '尘肺病康复站数', value: 0 }
    ]
});

const seriesData = ref([])

watchEffect(() => {
    seriesData.value = props.seriesData.map((item: { name: string, value: number }) => {
        let value = item.value.toString().split('')
        while (value.length < 2) {
            value.unshift('0')
        }
        return {
            title: item.name,
            value
        }
    })
})

</script>

<style scoped lang="scss">
.OrgStatistic-outerContainer {
    width: 100%;
    height: 100%;

    .OrgStatistic {
        width: 100%;
        height: 100%;
        display: flex;
        flex-wrap: wrap;

        justify-content: space-evenly;
        align-items: flex-end;

        .OrgStatistic-item {
            width: 33.33%;
            display: flex;
            justify-content: center;
        }

        .OrgStatistic-card {
            width: vw(198.8);
            height: vh(84);

            flex: 1;

            background-image: url("@/assets/images/cardBG.svg");
            background-repeat: no-repeat;
            background-size: 100% 100%;

            display: flex;
            flex-direction: column;
            justify-content: space-evenly;
            align-items: center;

            .number {
                display: flex;
                gap: vw(3);

                .letter {
                    width: vw(43.6);
                    height: vh(42.8);
                    background-image: url("@/assets/images/cardNumber.svg");
                    background-repeat: no-repeat;
                    background-size: 100% 100%;

                    display: flex;
                    justify-content: center;
                    align-items: center;

                    font-family: Source Han Sans;
                    font-size: vh(40);
                    font-weight: bold;
                    line-height: normal;
                    letter-spacing: 0px;
                    color: #FFFFFF;
                }
            }

            .title {
                font-family: 思源黑体;
                font-size: vh(14);
                font-weight: normal;
                line-height: normal;
                text-align: center;
                letter-spacing: 0px;
                color: #FFFFFF;
            }
        }
    }


}
</style>