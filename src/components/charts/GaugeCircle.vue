<template>
  <div class="container">
    <div ref="gaugeChartRef" class="gaugeChart"></div>
  </div>
</template>
<script setup lang="ts" name="gauge">
import * as echarts from 'echarts';
import { ref, onMounted, onBeforeUnmount, watch, markRaw, nextTick } from "vue";
import { emitter } from "@/utils/mitt";
import { debounce } from 'lodash';
import img from '@/assets/images/组 <EMAIL>';

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { offsetHeight } = storeToRefs(dashBoardStore)

const props = withDefaults(defineProps<{ title?: string, data?: any }>(),
  {
    title: 'LineChart',
    data: 0, // 0~100
  })

let labelData:any[] = [];

const option = {
  series: [
    {
      type: 'pie',
      startAngle: -90,
      radius: ['80%', '92%'],
      padAngle: 4,
      label: {
        position: 'center',
        formatter: '{big|' + props.data + '}{small|%}',
        rich: {
          big: {
            color: '#FFF',
            fontSize: 24,
            fontFamily: 'DS-digital',
            fontWeight: 'bold',
            verticalAlign: 'bottom',

          },
          small: {
            color: '#FFF',
            fontSize: 16,
            fontFamily: 'DS-digital',
            fontWeight: 'bold',
            verticalAlign: 'bottom',

          }
        },
      },
      data: labelData,
      hoverAnimation: false,
      silent: true,
    },

  ],
  graphic: {
    elements: [
      {
        type: "image",
        style: {
          image: img,
          width: '100%',
          height: '100%',
        },
        left: 'center',
        top: 'center',
        z: 1
      },
      // {
      //   type: "text",
      //   left: "center",
      //   top: "center",
      //   style: {
      //     text: "67.1%",
      //     textAlign: "center",
      //     fill: "#FFF",
      //     width: 30,
      //     height: 30,
      //     fontSize: 36,
      //     fontFamily: 'DS-digital',
      //     fontWeight: 'bold',
      //   },
      //   z: 3,
      // },
    ]
  }
};


const gaugeChartRef = ref()
const chartInstance = ref();

// 绘制(更新)
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// 初始化
const initBar = () => {
  if (!gaugeChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(gaugeChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(gaugeChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
}

// resize
const resizeChartfun = debounce(() => {
  option.series[0].label.rich.big.fontSize = offsetHeight.value / 1080 * 25
  option.series[0].label.rich.small.fontSize = offsetHeight.value / 1080 * 16
  nextTick(() => {
    chartInstance.value.resize();
    draw()
  });
}, 500)

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    const value = props.data;
    const len = Math.ceil(value / 2);
    // console.log('value:', value, 'len:', len);
    labelData = [];
    for (var i = 0; i < 50; ++i) {
      labelData.push({ value: 1, name: i, itemStyle: { color: 'rgba(0,209,228,0)', } });
    }
    for (var i = 0; i < labelData.length; ++i) {
      if (labelData[i].name < len) {
        labelData[i].itemStyle = { color: 'rgba(37, 196, 248, 0.3)' }
      }
    }
    option.series[0].label.formatter = '{big|' + value + '}{small|%}';
    option.series[0].data = labelData;
    draw()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  initBar()
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
})

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  @media screen and (max-width: 1960px) {
    font-size: 10px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 8px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 6px;
  }

  .gaugeChart {
    width: 100%;
    height: 100%;
  }
}
</style>
