<template>
  <div class="list-container">
    <div class="list-header">
      <div :style="{ width: props.leftWidth }" class="textAlignCenter"><span>{{ props.leftTitle }}</span></div>
      <div :style="{ width: props.centerWidth }" class="textAlignCenter"><span>{{ props.centerTitle }}</span></div>
      <div :style="{ width: props.rightWidth }" class="textAlignCenter"><span>{{ props.rightftTitle }}</span></div>
    </div>
    <Vue3SeamlessScroll v-if="props.seriesData.length > 0" ref="seamlessScrollRef" class="scroll-wrap"
      :list="props.seriesData" hover :limitMoveNum="props.seriesData.length" :step="0.2" :key="new Date().getTime()">
      <div v-for="(item, index) in props.seriesData" :key="index">
        <div class="list-item">
          <div :style="{ width: props.leftWidth }" class="textAlignCenter"><span>{{ item.region }}</span></div>
          <el-tooltip class="box-item" effect="dark" placement="top" :append-to-body="true"
            popper-class="custom-tooltip" :teleported="false" >
            <div :style="{ width: props.centerWidth }" class="textE textAlignCenter">
              <span>{{ item.employer }}</span>
            </div>
            <template #content>
              <div style="width: 10vw;text-align: center;">{{ item.employer }}</div>
            </template>
          </el-tooltip>
          <div :style="{ width: props.rightWidth }" class="textAlignCenter"><span>{{ item.date }}</span></div>
        </div>
      </div>
    </Vue3SeamlessScroll>
    <div v-else class="scroll-wrap">
      <div class="list-item" style="display: flex;justify-content: center;">
        <span>暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="List">
import { Vue3SeamlessScroll } from "vue3-seamless-scroll";

const props = withDefaults(defineProps<{
  seriesData?: any;
  leftTitle?: string, leftWidth?: string;
  centerTitle?: string, centerWidth?: string;
  rightftTitle?: string, rightWidth?: string
}>(), {
  seriesData: [],
  leftTitle: '地区',
  leftWidth: '30%',
  centerTitle: '企业名称',
  centerWidth: '40%',
  rightftTitle: '时间',
  rightWidth: '30%'
});

// const listData = ref([
//   { region: '温州市/平阳县1', employer: '公司名称公司名称公司名称公司名称', date: '2024-24-24' },
//   { region: '温州市/平阳县2', employer: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', date: '2024-24-24' },
//   { region: '温州市/平阳县3', employer: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Error ullam placeat harum nesciunt hic est soluta accusamus enim eaque optio inventore, dolor officia perferendis quae natus, architecto similique totam impedit!', date: '2024-24-24' },
//   { region: '温州市/平阳县4', employer: '公司名称公司名称公司名称公司名称', date: '2024-24-24' },
//   { region: '温州市/平阳县5', employer: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', date: '2024-24-24' },
//   { region: '温州市/平阳县6', employer: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Error ullam placeat harum nesciunt hic est soluta accusamus enim eaque optio inventore, dolor officia perferendis quae natus, architecto similique totam impedit!', date: '2024-24-24' },
//   { region: '温州市/平阳县7', employer: 'Lorem ipsum, dolor sit amet consectetur adipisicing elit. Error ullam placeat harum nesciunt hic est soluta accusamus enim eaque optio inventore, dolor officia perferendis quae natus, architecto similique totam impedit!', date: '2024-24-24' },
// ])
</script>

<style lang="scss" scoped>
.textAlignCenter {
  text-align: center;
}

.list-container {
  width: 100%;
  height: 100%;
  // width: vw(395);
  // height: calc(vh(245) - vw(27) - 4px);
  // height: calc(vh(218) - 4px);
  padding: 0 vw(10);
  display: flex;
  flex-direction: column;

  .list-header {
    width: 100%;
    font-size: vh(12);
    height: 2.25em;
    line-height: 2.25em;
    background-color: rgba(15, 128, 131, 1);
    color: #FFFFFF;
    border-radius: 4px;
    padding: 0 0.66em;
    display: flex;

    justify-content: space-between;
    margin-top: vh(9);
    margin-bottom: vh(11 - 7);
  }

  .scroll-wrap {
    width: 100%;
    overflow: hidden;
    height: calc(100% - vh(12 *2.25));
    // height: vh(140);

    .list-item {
      width: 100%;
      font-size: vh(10);
      height: 2.1em;
      line-height: 2.1em;
      background-color: rgba(14, 117, 121, 0.29);
      color: #FFFFFF;
      border-radius: 4px;
      margin-top: vh(7);
      padding: 0 0.66em;
      display: flex;
      justify-content: space-between;
    }
  }
}

.textE {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.custom-tooltip {
  z-index: 9999 !important;
}
</style>