<template>
  <div class="rank-container">
    <div ref="scatterChartRef" class="scatterChart"></div>
  </div>
</template>
<script setup lang="ts" name="RegionalRank">
import * as echarts from 'echarts';
import { ref, onMounted, onBeforeUnmount, watch, markRaw, nextTick } from "vue";
import { emitter } from "@/utils/mitt";
import { debounce } from 'lodash';

const props = withDefaults(defineProps<{ title?: string, axisData?: any, seriesData?: any }>(),
  {
    title: 'scatterChart',
    axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
    seriesData: [90, 85, 80, 75, 70, 65, 60, 55, 50, 45, 40],
  })

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { offsetHeight } = storeToRefs(dashBoardStore)

import img from '@/assets/images/scatterPoint.png';

const maxValue = ref(1)

const option = {
  color: ['00B5F1'],
  grid: {
    left: '9%',
    top: '9%',
    bottom: '12%',
  },
  tooltip: {
    trigger: 'item',
    axisPointer: {
      type: 'shadow'
    },
    className: 'custom-tooltip-box-scatter',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 12
    },
    formatter: `
    <div style="
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
      justify-content: space-around;"
    >
      <p>{a}</p>
      <p>{b}：<span style="color:#17E3E3;font-weight:bold;">{c}</span></p>
    </div>
    `
  },
  xAxis: {
    data: [],
    splitLine: { show: false },
    axisLine: { show: false },
    axisTick: { show: false },
    axisLabel: {
      fontSize: 8,
      rotate: 30,
      interval: 0,
      color: '#fff',
    }
  },
  yAxis: {
    show: false,
  },
  series: [
    {
      name: props.title,
      color: 'rgba(0,0,0,0)',
      data: [],
      type: 'line',
      symbol: 'image://' + img,
      symbolSize: (value: number) => {
        return (value / maxValue.value) * 18 + 12
      },
      label: {
        show: true,
        position: 'inside',
        color: '#fff',
        fontSize: 8,
        emphasis: {
          fontSize: 10,
        }
      }
    }
  ]
};

const scatterChartRef = ref()
const chartInstance = ref();

// 绘制(更新)
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// 初始化
const initScatter = () => {
  if (!scatterChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(scatterChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(scatterChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
}

// resize
const resizeChartfun = debounce(() => {
  nextTick(() => {
    option.series[0].symbolSize = (value: number) => {
      return ((value / maxValue.value) * 18 + 12) * offsetHeight.value / 1080
    }
    // option.xAxis.axisLabel.fontSize = (document.getElementsByClassName('layout-container')[0] as HTMLElement).offsetHeight / 1080 * 10
    chartInstance.value.resize();
    draw()
  });
}, 500)

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option.xAxis.data = props.axisData
    option.series[0].data = props.seriesData
    const dataMax = Math.max(...props.seriesData)
    dataMax > 0 ? maxValue.value = dataMax : maxValue.value = 1
    // option.series[0].data = props.seriesData.sort((a: number, b: number) => b - a)
    draw()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  initScatter()
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
})

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.rank-container {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  @media screen and (max-width: 1960px) {
    font-size: 10px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 8px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 6px;
  }

  .scatterChart {
    width: 100%;
    height: 100%;
  }
}
</style>
<style lang="scss">
.custom-tooltip-box-scatter {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>
