<template>
  <div class="wrap">
    <div ref="lineChartRef" class="lineChart"></div>
  </div>
</template>
<script setup lang="ts" name="DoubleBar">
import * as echarts from 'echarts';
import type { EChartsType, EChartsOption } from 'echarts';
import { ref, onMounted, onBeforeUnmount, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'

import _ from 'lodash'

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { splitLineColor } = storeToRefs(dashBoardStore)

const props = withDefaults(defineProps<{ title?: string, seriesData?: any; axisData?: any; gap?: boolean; showNumber?: boolean; yAxis?: {} }>(), {
  title: 'DoubleBarChart',
  // axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
  // seriesData: [290, 130, 230, 330, 430, 530, 290, 130, 230, 330, 430, 530],
  gap: true,
  showNumber: false,
  yAxis: () => ({})
});

const lineChartRef = ref()
const chartInstance = ref<EChartsType>();

const unit = ref(1)

function random(min: number, max: number) {
  return parseInt(Math.random() * (max - min) + min + '');
}

let xData = ['重点行业', '纺织行业', '制造业', '采矿', '手工'],
  yData = [],
  barData = [];

for (let i = 0; i < xData.length; i++) {
  let value = random(5, 100);
  yData.push(value);
  barData.push(parseInt((value / 3 * 2) * (random(1, 100) / 100) + ''));
}

const option = {
  grid: {
    top: '10%',
    left: '2.5%',
    bottom: '5%',
    right: '5%',
    containLabel: true,
  },
  // tooltip: {
  //   trigger: 'item',
  // },
  // animation: false,
  xAxis: {
    type: 'category',
    data: xData,
    axisTick: {
      alignWithLabel: true,
    },
    axisLabel: {
      textStyle: {
        color: '#ddd',
        fontSize: 8,
        interval: 0,
      },
    },
    interval: 1,
  },
  yAxis: {
    name: '',
    type: 'value',
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: splitLineColor.value,
      },
    },
    axisLabel: {
      color: '#D9D9D9',
      fontSize: 12,
    },
  },
  series: [
    // 佐助
    {
      name: '底部圆',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [16, 8],
      symbolOffset: ['-60%', 5],
      z: 1,
      color: '#25C4F8',
      data: yData,
    },
    {
      name: '左柱',
      type: 'bar',
      barWidth: '16',
      barGap: '20%',
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 8
      },
      silent: true,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(37, 196, 248, 0)',
          },
          {
            offset: 1,
            color: '#25C4F8',
          },
        ]),
        opacity: 1,
      },
      data: yData.map((item) => item + 20),
      z: 2,
    },
    {
      name: '上部圆',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [16, 8],
      symbolOffset: ['-60%', -4],
      symbolPosition: 'end',
      z: 3,

      // color: '#25C4F8',
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(37, 196, 248, 0)',
        },
        {
          offset: 1,
          color: '#17E3E3',
        },
      ]),
      //  linear-gradient(0deg, #17E3E3 0%, rgba(37, 196, 248, 0) 100%);
      data: yData.map((item) => item + 20),
    },
    // 右柱
    {
      name: '底部圆',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [16, 8],
      symbolOffset: ['60%', 5],
      z: 1,
      color: '#FFD33C',
      data: yData,
    },
    {
      name: '右柱',
      type: 'bar',
      barWidth: '16',
      // barGap: '20%',
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 8
      },
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(248, 211, 3, 0)',
          },
          {
            offset: 1,
            color: '#FFD33C',
          },
        ]),
        opacity: 1,
      },
      data: yData,
      z: 2,
    },
    {
      name: '上部圆',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [16, 8],
      symbolOffset: ['60%', -4],
      symbolPosition: 'end',
      z: 3,
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(248, 211, 3, 0)',
        },
        {
          offset: 1,
          color: '#FFD33C',
        },
      ]),
      data: yData,
    },
  ],
};

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option as EChartsOption);
  }
};

//初始化
const init = () => {
  if (!lineChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(lineChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(lineChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    // option.xAxis[0].data = props.axisData;  // x轴数据
    // option.series[0].data[0].value = props.data;
    // option.series[1].data[0].value = props.data;
    // 双柱是否重叠
    option.series[0].symbolOffset = props.gap ? ['-60%', 5] : ['0%', 5];
    option.series[1].barGap = props.gap ? '20%' : '-100%';
    option.series[2].symbolOffset = props.gap ? ['-60%', -4] : ['0%', -4];
    option.series[3].symbolOffset = props.gap ? ['60%', 5] : ['0%', 5];
    option.series[4].barGap = props.gap ? '20%' : '-100%';
    option.series[5].symbolOffset = props.gap ? ['60%', -4] : ['0%', -4];
    // 顶部是否显示数值
    // @ts-ignore
    option.series[1].label.show = props.showNumber;
    // @ts-ignore
    option.series[4].label.show = props.showNumber;

    option.yAxis = _.merge(option.yAxis, props.yAxis);

    draw()
  },
  { immediate: true, deep: true }
)

const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.lineChart {
  width: 100%;
  height: 100%;
}

.wrap {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;
}

.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>