<template>
  <div class="container">
    <!-- <div class="ring"></div> -->
    <img src="@/assets/images/ringPie.svg" class="ringImg">
    <img class="icon" :src="iconSrc"></img>
    <div class="title">{{ props.title || '矿山' }}</div>
    <div class="total">{{ props.total || 0 }}</div>
    <div class="value">{{ props.value || 0 }}</div>
  </div>
</template>

<script setup lang="ts" name="RingPie">
import { computed } from 'vue';
import icon1 from '@/assets/images/矿山.svg'

const props = defineProps<{ title?: string, icon?: any, total?: number, value?: number }>()

const iconSrc = computed(() => {
  return props.icon || icon1;
});
</script>

<style lang="scss" scoped>
.container {
  width: 80%;
  height: 100%;
  position: relative;
  left: 10%;
  display: flex;
  flex-direction: column;
  align-items: center;
  

  .ringImg {
    width: 100%;
    height: 100%;
    position: absolute;
  }

  .ring {
    width: 100%;
    height: 100%;
    background-image: url("@/assets/images/ringPie.svg");
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .icon {
    width: vh(40);
    height: vh(40);
    position: relative;
    top: 5%;
  }

  .title {
    font-size: vh(13);
    letter-spacing: 0.08px;
    font-family: YouSheBiaoTiHei;
    font-weight: normal;
    line-height: normal;
    color: #FFFFFF;

    position: absolute;
    top: 50%;
    transform: translateY(-80%);
  }

  .total{
    font-size: vh(12);
    letter-spacing: 0.06px;
    font-family: DS-Digital;
    font-weight: normal;
    line-height: normal;
    color: #FFFFFF;

    position: absolute;
    bottom: 25%;
    transform: translateY(50%);
  }

  .value{
    font-size: vh(12);
    letter-spacing: 0.06px;
    font-family: DS-Digital;
    font-weight: normal;
    line-height: normal;
    color: #FFFFFF;

    position: absolute;
    bottom: 50%;
    right: -14%;
    transform: translateY(50%);
  }
}
</style>