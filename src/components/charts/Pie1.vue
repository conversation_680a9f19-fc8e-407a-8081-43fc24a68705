<template>
  <div class="Pie1-wrap">
    <div ref="pieChartRef" class="pieChart"></div>
  </div>
</template>

<script setup lang="ts" name="Pie1">
import * as echarts from 'echarts';
import type { EChartsType } from 'echarts';
import { ref, onMounted, onBeforeUnmount, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'
// 
import img from '@/assets/images/pie1.svg'
// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { offsetHeight, chartColor, fontSizeNormal } = storeToRefs(dashBoardStore)


const props = withDefaults(defineProps<{ title?: string, seriesData?: any; centerText?: string }>(), {
  title: 'Pie1C<PERSON>',
  seriesData: [
    // { value: 5127, name: '杭州' },
    // { value: 6773, name: '宁波' },
    // { value: 4868, name: '温州' },
    // { value: 9487, name: '嘉兴' },
    // { value: 2332, name: '湖州' },
    // { value: 2995, name: '绍兴' },
    // { value: 2881, name: '金华' },
    // { value: 917, name: '衢州' },
    // { value: 613, name: '舟山' },
    // { value: 5617, name: '台州' },
    // { value: 1387, name: '丽水' }
  ],
  centerText: '企业总数'
});


const imgWidth = 221

const pieChartRef = ref()
const chartInstance = ref<EChartsType>();

const option = {
  color: chartColor.value,
  tooltip: {
    trigger: 'item',
    className: 'custom-tooltip-box',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 12
    },
  },

  legend: [
    {
      data: props.seriesData.map((item: any) => item.name).slice(0, 5),
      orient: 'vertical',
      top: 'center',
      left: '0',
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12,
      },
      itemWidth: 10,
      itemHeight: 8,
      formatter: function (name: string) {
        const findItem = props.seriesData.find((item: any) => item.name === name)
        return name + '：' + findItem.value;
      }
    },
    {
      data: props.seriesData.map((item: any) => item.name).slice(5, props.seriesData.map((item: any) => item.name).length),
      orient: 'vertical',
      right: '0',
      top: 'center',
      textStyle: {
        color: '#FFFFFF',
        fontSize: 12,
      },
      itemWidth: 10,
      itemHeight: 8,
      formatter: function (name: string) {
        const findItem = props.seriesData.find((item: any) => item.name === name)
        return name + '：' + findItem.value;
      }
    },
  ],
  series: [
    {
      zlevel: 10,
      name: props.title,
      type: 'pie',
      radius: ['27%', '55%'],
      avoidLabelOverlap: true,
      label: {
        show: false,
        position: 'center',
        color: '#FFFFFF',
      },
      emphasis: {
        label: {
          show: false,
          fontSize: 20,
          fontWeight: 'bold',
        }
      },
      labelLine: {
        show: false
      },
      data: props.seriesData
    }
  ],
  graphic: {
    elements: [
      {
        type: "image",
        zlevel: 1,
        style: {
          image: img,
          width: imgWidth,
          height: imgWidth,
        },
        left: 'center',
        top: 'center',
      },
      {
        type: "text",
        left: "center",
        top: "center",
        style: {
          text:
            `${props.centerText}\n`
            + props.seriesData.reduce((sum: number, item: { value: number, name: string }) => {
              return sum + item.value
            }, 0),
          textAlign: "center",
          fill: "#FFF",
          width: 30,
          height: 30,
          fontSize: 12,
        },
      },
    ]
  }
};


// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

//初始化
const init = () => {
  let imgWidth = offsetHeight.value / 1080 * 160
  option.graphic.elements[0].style.width = imgWidth

  if (!pieChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(pieChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(pieChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    const len = props.seriesData.length
    const half = Math.floor(len / 2)
    option.legend[0].data = props.seriesData.map((item: any) => item.name).slice(0, half)
    option.legend[1].data = props.seriesData.map((item: any) => item.name).slice(half, len)
    option.series[0].data = props.seriesData
    option.graphic.elements[1].style.fontSize = 4 / props.centerText.length * 12
    option.graphic.elements[1].style.text =
      props.seriesData.reduce((sum: number, item: { value: number, name: string }) => {
        return sum + item.value
      }, 0)
      + `\n${props.centerText}`;
    draw()
  },
  { immediate: true, deep: true }
)


const resizeChartfun = debounce(() => {
  // const offsetHeight = (document.getElementsByClassName('layout-container')[0] as HTMLElement).offsetHeight;
  let imgWidth = offsetHeight.value / 1080 * 160
  option.graphic.elements[0].style.width = imgWidth
  option.graphic.elements[1].style.fontSize = 4 / props.centerText.length * 12 * offsetHeight.value / 1080
  option.legend[0].textStyle.fontSize = fontSizeNormal.value * offsetHeight.value / 1080
  option.legend[1].textStyle.fontSize = fontSizeNormal.value * offsetHeight.value / 1080
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.pieChart {
  width: 100%;
  height: 100%;
}

.Pie1-wrap {
  width: 100%;
  height: 100%;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;
}

.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>