<template>
  <div class="wrap">
    <div ref="lineChartRef" class="lineChart"></div>
  </div>
</template>
<script setup lang="ts" name="DoubleBar">
import * as echarts from 'echarts';
import type { EChartsType, EChartsOption } from 'echarts';
import { ref, onMounted, onBeforeUnmount, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'
import _ from 'lodash'
import { getUnitShrink } from '@/utils/tools'

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { splitLineColor } = storeToRefs(dashBoardStore)

const props = withDefaults(
  defineProps<{
    title?: string,
    axisData?: any;
    bar?: number[],
    line?: number[],
    gap?: boolean;
    showNumber?: boolean,
    yAxisName?: string,
    tooltip?: any,
    barWidth?: number,
    seriesName?: string[],
  }>(), {
  title: 'DoubleBarWithLine',
  // axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
  gap: true,
  showNumber: false,
  bar: () => ([]),
  line: () => ([]),
  yAxisName: '',
  tooltip: () => ({}),
  barWidth: 16,
  seriesName: () => (['报告数', '超标率']),
});

const lineChartRef = ref()
const chartInstance = ref<EChartsType>();

const unit = ref(1)

const color = ['#19CCCC', '#FFD33C', '#00B5F1', '#8A7EEE', '#17BC84']
const areaColor = ['rgba(25, 204, 204, 0.33)', 'rgba(239, 199, 58, 0.33)', 'rgba(0, 181, 241, 0.39)', ' rgba(138, 126, 238, 0.39)', 'rgba(23, 188, 132, 0.39)']

function random(min: number, max: number) {
  return parseInt(Math.random() * (max - min) + min + '');
}

let xData = ['重点行业', '纺织行业', '制造业', '采矿', '手工'],
  yData = [],
  barData = [];

for (let i = 0; i < xData.length; i++) {
  let value = random(5, 100);
  yData.push(value);
  barData.push(parseInt((value / 3 * 2) * (random(1, 100) / 100) + ''));
}

const option = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    className: 'custom-tooltip-box-DBAL',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 8
    },
  },
  legend: [
    // 圆角矩形
    {
      data: [
        { name: '报告数', itemStyle: { color: color[0] } },
        { name: '超标率', itemStyle: { color: color[1] } },
      ],
      textStyle: {
        color: '#ccc'
      },
      icon: 'roundRect',
      itemHeight: 8,
      itemWidth: 10,
      right: '10%',
    },
  ],
  grid: {
    top: '20%',
    left: '2.5%',
    bottom: '5%',
    right: '2.5%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      alignWithLabel: true,
    },
    axisLabel: {
      textStyle: {
        color: '#ddd',
        fontSize: 8,
      },
      interval: 0,
      width: 32,
      overflow: 'break',
    },
  },
  yAxis: [
    {
      name: '',
      nameTextStyle: {
        padding: [0, 30, 0, 0]
      },
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: splitLineColor.value,
        },
      },
      axisLabel: {
        color: '#D9D9D9',
        fontSize: 12,
      },
      position: "left",
    },
    {
      name: '%',
      nameTextStyle: {
        padding: [0, 0, 0, 30]
      },
      type: 'value',
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: '#D9D9D9',
        fontSize: 12,
      },
      position: "right"
    }
  ],
  series: [
    {
      name: '报告数',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [props.barWidth, props.barWidth * 0.5],
      symbolOffset: ['0', '50%'],
      color: '#25C4F8',
      data: yData,
      yAxisIndex: 0,
      z: 1,
    },
    {
      name: '报告数',
      type: 'bar',
      barWidth: props.barWidth,
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 8
      },
      silent: true,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(37, 196, 248, 0)',
          },
          {
            offset: 1,
            color: '#25C4F8',
          },
        ]),
        opacity: 1,
      },
      data: yData.map((item) => item + 20),
      yAxisIndex: 0,
      z: 2,
    },
    {
      name: '报告数',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [props.barWidth, props.barWidth * 0.5],
      symbolOffset: ['0%', '-50%'],
      symbolPosition: 'end',
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(37, 196, 248, 0)',
        },
        {
          offset: 1,
          color: '#17E3E3',
        },
      ]),
      data: yData.map((item) => item + 20),
      yAxisIndex: 0,
      z: 3,
    },
    {
      yAxisIndex: 1,
      name: '超标率',
      type: 'line',
      smooth: true,
      data: [],
      symbolSize: 0,
      label: { show: false },
      lineStyle: { width: 3, color: color[1] },
      areaStyle: { //区域填充样式
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(239, 199, 58, 0.6727)',
        },
        {
          offset: 1,
          color: 'rgba(252, 227, 209, 0.2141)',
        }
        ], false),
      },
    },
  ],
};

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option as EChartsOption);
  }
};

//初始化
const init = () => {
  if (!lineChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(lineChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(lineChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    // 顶部是否显示数值
    // @ts-ignore
    option.series[1].label.show = props.showNumber;

    option.xAxis.data = props.axisData;  // x轴数据

    option.series[0].data = new Array(props.bar.length).fill(0);
    option.series[1].data = props.bar;
    option.series[2].data = props.bar;
    option.series[3].data = props.line;

    const res = getUnitShrink(props.bar)
    option.yAxis.name = res.unitName + props.yAxisName
    unit.value = res.unit

    option.tooltip = _.merge(option.tooltip, props.tooltip)

    option.series[0].name = props.seriesName[0]
    option.series[1].name = props.seriesName[0]
    option.series[2].name = props.seriesName[0]
    option.series[3].name = props.seriesName[1]
    option.legend[0].data = [
      { name: props.seriesName[0], itemStyle: { color: color[0] } },
      { name: props.seriesName[1], itemStyle: { color: color[1] } },
    ]


    draw()
  },
  { immediate: true, deep: true }
)

const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.lineChart {
  width: 100%;
  height: 100%;
}

.wrap {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;
}
</style>

<style lang="scss">
.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>