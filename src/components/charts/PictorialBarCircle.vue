<template>
  <div class="wrap">
    <div ref="lineChartRef" class="lineChart"></div>
  </div>
</template>
<script setup lang="ts" name="PictorialBarCircle">
import * as echarts from 'echarts';
import type { EChartsType, EChartsOption } from 'echarts';
import { ref, onMounted, onBeforeUnmount, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'
import { getUnitShrink } from '@/utils/tools'


// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { splitLineColor } = storeToRefs(dashBoardStore)

const props = withDefaults(defineProps<{
  title?: string,
  seriesData?: any;
  axisData?: any;
  yAxisName?: string,
  yAxisNameShow?: boolean; // y轴名称是否显示
  yAxisLabelShow?: boolean; // y轴标签是否显示
  yAxisLabelShrink?: boolean; // y轴标签是否缩小
  showYAxisSplitLine?: boolean; // 是否显示y轴分割虚线
}>(), {
  title: 'PictorialBarCircleChart',
  // axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
  // seriesData: [290, 130, 230, 330, 430, 530, 290, 130, 230, 330, 430, 530],
  yAxisName: '',
  yAxisNameShow: false,
  yAxisLabelShow: true,
  yAxisLabelShrink: true,
  showYAxisSplitLine: true,
});
const unit = ref(1)

const lineChartRef = ref()
const chartInstance = ref<EChartsType>();

const option = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    className: 'custom-tooltip-box',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 12
    },
    // 
    // formatter: function (params) {
    //   console.log(params)
    // },
  },
  grid: {
    top: '20%',
    left: '3%',
    right: '4%',
    bottom: '-0',
    containLabel: true
  },
  xAxis: {
    data: [],
    axisLabel: {
      color: '#D9D9D9',
      fontSize: 10,
      interval: 0,
    },
  },
  yAxis: {
    name: '',
    nameTextStyle: {
      color: '#D9D9D9',
    },
    type: 'value',
    max: (value: { min: number, max: number; }) => {  // 百位起最大值向上取整
      if (value.max <= 10) {
        return 10;
      }
      else return undefined;
    },
    splitLine: {
      show: props.showYAxisSplitLine,
      lineStyle: {
        type: 'dashed',
        color: splitLineColor.value,
      },
    },
    axisLabel: {
      show: props.yAxisLabelShow,
      color: '#D9D9D9',
      fontSize: 10,
      formatter(value: number) {
        if (value) {
          // 返回100以内的整数
          return Math.floor(value / unit.value)
        }
      },
    },
  },
  animationEasing: 'elasticOut',
  series: [
    {
      name: props.title,
      type: 'pictorialBar',
      symbol: "path://M0 3.8120381832122803a3.8120381832122803 3.8120381832122803 0 0 1 3.8120381832122803 -3.8120381832122803h7.873899936676025a3.8120381832122803 3.8120381832122803 0 0 1 3.8120381832122803 3.8120381832122803v0a3.8120381832122803 3.8120381832122803 0 0 1 -3.8120381832122803 3.8120381832122803h-7.873899936676025a3.8120381832122803 3.8120381832122803 0 0 1 -3.8120381832122803 -3.8120381832122803z",
      symbolSize: [15.5, 7.6],
      symbolRepeat: true,
      barMinHeight: 7.6,
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 8
      },
      itemStyle: {
        color: '#18CACA'
        // color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        //   {
        //     offset: 0,
        //     color: "rgba(255, 211, 60, 0.8)"
        //   },
        //   {
        //     offset: 0.4,
        //     color: "#18CACA"
        //   },
        //   {
        //     offset: 1,
        //     color: "#318CD6"
        //   },
        // ])
        // background: linear-gradient(180deg, rgba(255, 211, 60, 0.8) 0%, #18CACA 40%, #18CACA 79%), linear-gradient(180deg, #8BF1F0 0%, #318CD6 100%);
      },
      data: []
    }
  ]
};

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option as EChartsOption);
  }
};

//初始化
const init = () => {
  if (!lineChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(lineChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(lineChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    const data = JSON.parse(JSON.stringify(props.seriesData))

    option.series[0].name = props.title
    option.xAxis.data = props.axisData
    option.series[0].data = data

    option.yAxis.name = props.yAxisName
    if (props.yAxisLabelShrink) {
      const res = getUnitShrink(data)
      option.yAxis.name = res.unitName + props.yAxisName
      unit.value = res.unit
    }

    draw()
  },
  { immediate: true, deep: true }
)

const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.lineChart {
  width: 100%;
  height: 100%;
}

.wrap {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;
}
</style>

<style lang="scss">
.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>