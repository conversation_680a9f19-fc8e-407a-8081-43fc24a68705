<template>
  <div class="rank-container">
    <div ref="barChartRef" class="barChart"></div>
  </div>
</template>
<script setup lang="ts" name="RegionalRank">
import * as echarts from 'echarts';
import { ref, onMounted, onBeforeUnmount, watch, markRaw, nextTick } from "vue";
import { emitter } from "@/utils/mitt";
import { debounce } from 'lodash';
import _ from 'lodash'
import { getUnitShrink } from '@/utils/tools'

const props = withDefaults(
  defineProps<{
    title?: string,
    axisData?: any,
    seriesData?: any,
    colorIndex?: number,
    yAxisName?: string;
    tooltip?: any;
    xAxisRotate?: boolean, // x轴旋转
    barWidth?: number;
    xAxisLabelShrink?: boolean; // x轴标签是否缩小
  }>(),
  {
    title: 'BarChart',
    axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
    seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    colorIndex: 0,
    yAxisName: '',
    tooltip: () => { },
    xAxisRotate: false,
    barWidth: 16,
    xAxisLabelShrink: false,
  })

const unit = ref(1)
const color = ['#19CCCC', '#FFD33C', '#00B5F1', '#8A7EEE', '#17BC84']

const option = {
  // color: '#01C4D3',
  color: color[props.colorIndex],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    className: 'custom-tooltip-box',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 12
    },
    formatter: `
    <div style="
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
      justify-content: space-around;"
    >
      <p>{a1}</p>
      <p>{b1}：<span style="color:#17E3E3;font-weight:bold;">{c1}</span></p>
    </div>
    `
  },
  grid: {
    top: '20%',
    left: '3%',
    right: '4%',
    bottom: '-0',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      alignWithLabel: true, // true：标签位于刻度线正下方；false：标签位于2个刻度线中间
    },
    // axisLine: {
    //   show: false
    // },
    axisLabel: {
      color: '#ddd',
      fontSize: 8,
      interval: 0,
      width: 36,
      overflow: 'break',

      margin: props.xAxisRotate ? 12:8,
      rotate: props.xAxisRotate ? 30 : 0,
      textShadowOffsetX: props.xAxisRotate ? 12 : 0,
      padding: [0, props.xAxisRotate ? -8 : 0, 0, 0]
    },
  },
  yAxis: {
    name: '',
    type: 'value',
    boundaryGap: [0, 0.01],
    max: (value: { min: number, max: number; }) => {  // 数据值很小时，设置最大值为10
      if (value.max <= 10) {
        return 10;
      }
      else return undefined;
    },
    // 分隔线
    splitLine: {
      lineStyle: {
        type: 'dashed',
        // color: '#D9D9D9',
        color: '#333'
      },
    },
    // 标签文字颜色
    axisLabel: {
      color: '#D9D9D9',
      fontSize: 10,
      formatter(value: number) {
        if (value) {
          // 返回100以内的整数
          return Math.floor(value / unit.value)
        }
      },
    },
  },

  series: [
    // 底部圆
    {
      data: [],
      type: 'pictorialBar',
      symbol: 'diamond',
      symbolOffset: [0, '50%'],
      symbolSize: [props.barWidth, props.barWidth * 0.5],
      itemStyle: {
        color: {
          x: 0, y: 0, x2: 1, y2: 0,
          type: 'linear',
          colorStops: [
            {
              offset: 0,
              color: '#108085'
            },
            {
              offset: 0.5,
              color: '#108085'
            },
            {
              offset: 0.5,
              color: '#18CACA'
            },
            {
              offset: 1,
              color: '#18CACA'
            }
          ]
        }
      }
    },
    {
      // bar
      name: props.title,
      data: [200, 85, 112, 275, 305, 415, 741, 405],
      type: 'bar',
      barWidth: props.barWidth,
      itemStyle: {
        color: {
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          type: 'linear',
          global: false,
          colorStops: [
            {
              offset: 0,
              color: '#108085'
            },
            {
              offset: 0.5,
              color: '#108085'
            },
            {
              offset: 0.5,
              color: '#18CACA'
            },
            {
              offset: 1,
              color: '#18CACA'
            }
          ]
        }
      },
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 8
      }
    },

    // 顶部圆
    {
      data: [200, 85, 112, 275, 305, 415, 741, 405],
      type: 'pictorialBar',
      symbolPosition: 'end',
      symbol: 'diamond',
      symbolOffset: [0, '-50%'],
      symbolSize: [props.barWidth, props.barWidth * 0.5],
      zlevel: 2,
      color: '#44FFFF'
    }
  ],
}

const barChartRef = ref()
const chartInstance = ref();

// 绘制(更新)
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// 初始化
const initBar = () => {
  if (!barChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(barChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(barChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
}

// resize
const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value.resize();
    draw()
  });
}, 500)

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option.xAxis.data = props.axisData
    const data = JSON.parse(JSON.stringify(props.seriesData))
    option.series[0].data = props.seriesData.map(() => { return 1 })
    option.series[1].data = data
    option.series[2].data = data

    const res = getUnitShrink(data)
    option.yAxis.name = res.unitName + props.yAxisName
    unit.value = res.unit

    option.tooltip = _.merge(option.tooltip, props.tooltip)

    draw()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  initBar()
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
})

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.rank-container {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  @media screen and (max-width: 1960px) {
    font-size: 10px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 8px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 6px;
  }

  .barChart {
    width: 100%;
    height: 100%;
  }
}

.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>
