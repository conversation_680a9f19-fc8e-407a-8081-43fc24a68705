<template>
  <div class="wrap">
    <div ref="lineChartRef" class="lineChart"></div>
  </div>
</template>
<script setup lang="ts" name="ProgressBar">
import * as echarts from 'echarts';
import type { EChartsType, EChartsOption } from 'echarts';
import { ref, onMounted, onBeforeUnmount, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { fontColor } = storeToRefs(dashBoardStore)

const props = withDefaults(defineProps<{ title?: string, seriesData?: any; axisData?: any; maxData?: number }>(), {
  title: 'ProgressBarChart',
  axisData: ['矽尘', '矽尘', '矽尘', '矽尘', '矽尘', '矽尘',],
  seriesData: [144, 122, 99, 64, 39, 27],
  maxData: 150
});

const lineChartRef = ref()
const chartInstance = ref<EChartsType>();

const color = ['#00B5F1', '#19CCCC', '#FABF00', '#8A7EEE', '#17BC84', '#FE615A']
const colorBg = ['rgba(0, 181, 241, 0.1)', 'rgba(25, 204, 204, 0.1)', 'rgba(250, 191, 0, 0.1)', 'rgba(138, 126, 238, 0.1)', 'rgba(23, 188, 132, 0.1)', 'rgba(254, 97, 90, 0.1)']

const option = {
  // tooltip: {
  //   formatter: (params: any) => {
  //     return `<div class="custom-tooltip-box">
  //       <div style="color: #fff; font-size: 12px; font-weight: 500;">${params.value}%</div>
  //     </div>`
  //   },
  // },
  xAxis: {
    max: props.maxData,
    splitLine: {
      show: false
    },
    offset: 10,
    axisTick: {
      show: false
    },
    axisLine: {
      show: false
    },
    axisLabel: {
      show: false
    }
  },
  yAxis: [{
    type: "category",
    inverse: true,
    axisLabel: {
      verticalAlign: "bottom",
      align: "left",
      padding: [0, 10, 6, 9],
      textStyle: {
        fontSize: 12,
        color: fontColor.value[0],
        fontFamily: "DS-digital "
      }
    },
    // 纵坐标数据
    data: [
      '矽尘  96%',
      '矽尘  81%',
      '矽尘  66%',
      '矽尘  43%',
      '矽尘  26%',
      '矽尘  18%',
    ],

    yAxisIndex: 0,
    // 横坐标 分割线等取消显示
    axisTick: {
      show: false
    },
    axisLine: {
      show: false
    }
  },],
  grid: {
    top: '5%',
    bottom: "-5%",
    left: '4%',
    right: '6%',
  },
  series: [
    {
      // current data
      type: 'pictorialBar',
      symbol: 'rect',
      symbolRepeat: 'fixed',
      symbolMargin: "25%",
      symbolSize: [4, 6],
      symbolBoundingData: props.maxData,
      data: [
        { value: 144, itemStyle: { color: '#00B5F1' } },
        { value: 122, itemStyle: { color: '#19CCCC' } },
        { value: 99, itemStyle: { color: '#FABF00' } },
        { value: 64, itemStyle: { color: '#8A7EEE' } },
        { value: 39, itemStyle: { color: '#17BC84' } },
        { value: 27, itemStyle: { color: '#FE615A' } },
      ],
      symbolClip: true,
      z: 99,
    },
    {
      // full data
      type: 'pictorialBar',
      symbolRepeat: 'fixed',
      symbolMargin: "25%",
      symbol: 'rect',
      symbolSize: [4, 6],
      symbolBoundingData: props.maxData,
      data: [
        { value: 0, itemStyle: { color: 'rgba(0, 181, 241, 0.1)' } },
        { value: 0, itemStyle: { color: 'rgba(25, 204, 204, 0.1)' } },
        { value: 0, itemStyle: { color: 'rgba(250, 191, 0, 0.1)' } },
        { value: 0, itemStyle: { color: 'rgba(138, 126, 238, 0.1)' } },
        { value: 0, itemStyle: { color: 'rgba(23, 188, 132, 0.1)' } },
        { value: 0, itemStyle: { color: 'rgba(254, 97, 90, 0.1)' } },
      ],
      z: 9,
    }
  ]
};

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option as EChartsOption);
  }
};

//初始化
const init = () => {
  if (!lineChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(lineChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(lineChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option.yAxis[0].data = props.axisData.map((item: string, index: number) => {
      return item + '  ' + Math.round(props.seriesData[index] / props.maxData) + '%'
    })
    option.series[0].data = props.seriesData.map((item: number, index: number) => {
      return { value: item, itemStyle: { color: color[index] } }
    })
    option.series[1].data = props.seriesData.map((item: number, index: number) => {
      return { value: 0, itemStyle: { color: colorBg[index] } }
    })
    draw()
  },
  { immediate: true, deep: true }
)

const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.lineChart {
  width: 100%;
  height: 100%;
}

.wrap {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;
}
</style>

<style lang="scss">
.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>