<template>
  <div class="container">
    <div ref="chartRef" class="chart"></div>
  </div>
</template>
<script setup lang="ts" name="pyramid">
import * as echarts from 'echarts';
import { ref, onMounted, watch, markRaw, nextTick } from "vue";
import { emitter } from "@/utils/mitt";
import { debounce } from 'lodash';
import { generateGradientColors } from '@/utils/tools';
// pinia
import { storeToRefs } from 'pinia'
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { offsetHeight } = storeToRefs(dashBoardStore)

const props = withDefaults(defineProps<{ title?: string; axisData: string[]; seriesData: number[]; isPercent?: boolean, allRight?: boolean }>(),
  {
    title: 'Pyramidchart',
    // axisData: () => [],
    // seriesData: () => [],
    isPercent: false,
    allRight: false
  })

// 传入数组长度，返回线性渐变的颜色数组
const getColor = (len: number) => {
  const res: string[] = []
  const startColor = '#003139' // 起始颜色，最深色
  const endColor = '#0eb2d3'; // 结束颜色，最浅色

  let colorArr = generateGradientColors(startColor, endColor, len)

  const half = Math.ceil(len - 1 / 2)

  for (let i = 0; i < half; i++) {
    res.push(colorArr[i])
    res.push(colorArr[colorArr.length - 1 - i])
  }
  if (res.length < len) {
    res.push(colorArr[half])
  }

  return res;
}

// 
const getData = (axisData: string[], seriesData: number[]) => {
  const res = []
  for (let i = 0; i < seriesData.length; i++) {
    let value = (props.seriesData.length - i) * 100
    const item = seriesData[i] + (props.isPercent ? '%' : '')
    const obj = {
      value: [0, 0, 0, 0, 0, value, 0, 0, 0, 0],
      // name: axisData[i],
      symbol: 'circle',
      symbolSize: 1,
      label: {
        show: true,
        color: '#fff',
        position: [0, 0],
        formatter: (point: { value: number; name: string; }) => { },
      }
    }
    // 如果i是偶数
    if (i % 2 === 0 || props.allRight) {
      obj.value = [0, 0, 0, 0, value, value, value - 1, 0, 0, 0]
      obj.label.position = [-5, -10]
      obj.label.formatter = function (point: { value: number; name: string; }) {
        return point.value === value - 1 ? "—— " + axisData[i] + '  ' + item : ''
      }
    }
    else {
      obj.value = [0, 0, 0, 0, value - 1, value, value, 0, 0, 0]
      obj.label.position = [-55, -10]
      obj.label.formatter = function (point: { value: number; name: string; }) {
        return point.value === value - 1 ? item + '  ' + axisData[i] + " ——" : ''
      }
    }
    res.push(obj)
  }
  return res
}

const option = {
  // color: ['#18CACA', '#92FAFD', '#139B9B', '#7BE5EF', '#097676', '#6CD6E6', '#046363', '#58C4DA', '#046060', '#45B2CE', '#034C4C'],
  radar: {
    name: {
      show: false
    },
    alignTicks: false,
    indicator: [
      { name: 'A', max: props.seriesData.length * 100 },
      { name: 'B', max: props.seriesData.length * 100 },
      { name: 'C', max: props.seriesData.length * 100 },
      { name: 'D', max: props.seriesData.length * 100 },
      { name: 'E', max: props.seriesData.length * 100 },
      { name: 'F', max: props.seriesData.length * 100 },
      { name: 'G', max: props.seriesData.length * 100 },
      { name: 'H', max: props.seriesData.length * 100 },
      { name: 'I', max: props.seriesData.length * 100 },
      { name: 'J', max: props.seriesData.length * 100 }
    ],
    center: [props.allRight ? "30%" : '50%', '10%'],
    radius: '160%',
    axisLine: {
      show: false
    },
    splitLine: {
      show: false
    },
    splitArea: {
      show: false
    }
  },
  series: [{
    type: 'radar',
    areaStyle: {
      opacity: 1,
      shadowBlur: 1,
      shadowColor: 'rgba(0,0,0,.5)',
    },
    silent: true,
    data: []
  }],
};

const chartRef = ref()
const chartInstance = ref();

// 绘制(更新)
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// 初始化
const initBar = () => {
  if (!chartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(chartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(chartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
}

// resize
const resizeChartfun = debounce(() => {
  nextTick(() => {
    // option.radar.name.textStyle.fontSize = offsetHeight.value / 1080 * 10;
    // option.radar.name.rich.value.fontSize = offsetHeight.value / 1080 * 20;
    chartInstance.value.resize();
    draw()
  });
}, 500)

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    const colors = getColor(props.axisData.length)
    const sortedData = props.seriesData.sort((a: number, b: number) => b - a)
    const res = getData(props.axisData, sortedData)
    option.series[0].data = res
    option.color = colors
    resizeChartfun()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  initBar()
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
})


</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  @media screen and (max-width: 1960px) {
    font-size: 10px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 8px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 6px;
  }

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>
