<template>
  <div class="wrap">
    <div ref="lineChartRef" class="lineChart"></div>
  </div>
</template>
<script setup lang="ts" name="PictorialBarTriangle">
import * as echarts from 'echarts';
import type { EChartsType, EChartsOption } from 'echarts';
import { ref, onMounted, onBeforeUnmount, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { splitLineColor } = storeToRefs(dashBoardStore)

const props = withDefaults(defineProps<{ title?: string, seriesData?: any; axisData?: any; }>(), {
  title: 'PictorialBarTriangleChart',
  // axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
  // seriesData: [290, 130, 230, 330, 430, 530, 290, 130, 230, 330, 430, 530],

});

const lineChartRef = ref()
const chartInstance = ref<EChartsType>();

const option = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    className: 'custom-tooltip-box',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 12
    },
    // 
    // formatter: function (params) {
    //   console.log(params)
    // },
  },
  grid: {
    top: '10%',
    left: '3%',
    right: '4%',
    bottom: '-0',
    containLabel: true
  },
  xAxis: {
    data: [],
    // 标签文字颜色
    axisLabel: {
      color: '#D9D9D9',
      fontSize: 10,
      interval: 0,
    },
  },
  yAxis: {
    max: (value: { min: number, max: number; }) => {  // 百位起最大值向上取整
      if (value.max <= 10) {
        return 10;
      }
      else return null;
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        // color: '#D9D9D9',
        color: splitLineColor.value,
      },
    },    // 标签文字颜色
    axisLabel: {
      color: '#D9D9D9',
    },
  },
  animationEasing: 'elasticOut',
  series: [
    {
      name: props.title,
      type: 'pictorialBar',
      symbol: "path://M10.5,0L21,17.9999L0,17.9999L10.5,0Z",
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 0.8, [
          {
            offset: 0,
            color: "rgba(255, 211, 60, 0.8)"
          },

          {
            offset: 0.8,
            color: "rgba(24, 202, 202, 1)"
          }
        ])
      },
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 8
      },
      data: []
    }
  ]
};

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option as EChartsOption);
  }
};

//初始化
const init = () => {
  if (!lineChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(lineChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(lineChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option.series[0].name = props.title
    option.xAxis.data = props.axisData
    option.series[0].data = props.seriesData
    draw()
  },
  { immediate: true, deep: true }
)


const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.lineChart {
  width: 100%;
  height: 100%;
}

.wrap {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;
}

.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>