<template>
  <div class="container">
    <div ref="chartRef" class="chart"></div>
  </div>
</template>
<script setup lang="ts" name="radar">
import * as echarts from 'echarts';
import { ref, onMounted, watch, markRaw, nextTick } from "vue";
import { emitter } from "@/utils/mitt";
import { debounce } from 'lodash';
// pinia
import { storeToRefs } from 'pinia'
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { offsetHeight } = storeToRefs(dashBoardStore)
import _ from 'lodash'

const props = withDefaults(defineProps<{
  title?: string,
  axisData?: any,
  seriesData?: any
  isPercent?: boolean
  maxValue?: number
  fontSize?: number
  radar?: any
}>(),
  {
    title: 'Radarchart',
    axisData: ['核医学', '放射治疗', 'X射线影像诊断', '介入放射学'], // 上、左、下、右
    seriesData: [75, 50, 35, 15],
    isPercent: true,
    maxValue: 100,
    fontSize: 10,
    radar: () => ({})
  })

const option = {
  textStyle: {
    color: '#fff',
  },
  tooltip: {
    show: false,
    trigger: 'item',
    className: 'custom-tooltip-box-doubleLine',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 8
    },
  },
  radar: {
    name: {
      textStyle: {
        color: '#fff',
        fontSize: 12,
      },
      rich: {
        // 定义富文本样式
        value: {
          color: '#fff',
          fontSize: 24,
          fontFamily: 'DS-digital',
        }
      }
    },
    center: ['50%', '50%'],
    radius: 55,
    startAngle: 90,
    axisNameGap: 2,
    shape: 'polygon',
    axisLine: {
      show: true,
      lineStyle: {
        color: 'rgba(37, 196, 248, 0.28)',
        width: 1,
      },
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: 'rgba(37, 196, 248, 0.28)',
        width: 1,
      },
    },
    splitArea: {
      show: true,
      areaStyle: {
        color: 'rgba(0, 0, 0,0)'
      },
    },
    indicator: [],
    min: 0,
    max: props.maxValue,
    // 禁用 alignTicks
    alignTicks: false,
    // 自定义刻度线数量
    splitNumber: 4,
  },
  series: {
    name: props.title,
    type: 'radar',
    symbol: 'none',
    areaStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(9, 127, 186, 0.65)' // 0% 处的颜色
        },
        {
          offset: 1,
          color: '#19CCCC' // 100% 处的颜色
        }
      ]),
    },
    emphasis: {
      areaStyle: {
        opacity: 0.8,
      }
    },
    lineStyle: {
      width: 0.6,
    },
    data: [{
      value: []
    }]
  }
};

const chartRef = ref()
const chartInstance = ref();

// 绘制(更新)
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// 初始化
const initBar = () => {
  if (!chartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(chartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(chartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
}

// resize
const resizeChartfun = debounce(() => {
  nextTick(() => {
    option.radar.name.textStyle.fontSize = offsetHeight.value / 1080 * props.fontSize;
    option.radar.name.rich.value.fontSize = offsetHeight.value / 1080 * props.fontSize * 2;
    chartInstance.value.resize();
    draw()
  });
}, 500)

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option.radar = _.merge(option.radar, props.radar)
    // 开根号
    const sqrtData = props.seriesData.map((item: number) => {
      return Math.sqrt(item) * 10
    })
    option.series.data[0].value = sqrtData
    option.radar.indicator = props.axisData.map((item: string, index: number) => {
      return {
        name: `${item}\n{value|${props.seriesData[index]}%}`,
        max: props.maxValue
      }
    })
    draw()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  initBar()
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
})


</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  @media screen and (max-width: 1960px) {
    font-size: 10px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 8px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 6px;
  }

  .chart {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.custom-tooltip-box-radar {
  width: calc(117.36px * 1.6);
  height: calc(55px * 1.6);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 12px !important;
  padding-right: 16px !important;
  display: none; // 防止首次渲染时出现

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // background-color: black;
    // opacity: 0.2;
    z-index: 1;
  }
}
</style>