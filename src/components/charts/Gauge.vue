<template>
  <div class="container">
    <div ref="gaugeChartRef" class="gaugeChart"></div>
  </div>
</template>
<script setup lang="ts" name="gauge">
import * as echarts from 'echarts';
import { ref, onMounted, onBeforeUnmount, watch, markRaw, nextTick } from "vue";
import { emitter } from "@/utils/mitt";
import { debounce } from 'lodash';
import img from '@/assets/images/圆形 64.svg';

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { offsetWidth } = storeToRefs(dashBoardStore)


const props = withDefaults(defineProps<{ title?: string, data?: any }>(),
  {
    title: 'gaugeChart',
    data: 0,
  })

const option = {
  series: [
    {
      type: 'gauge',
      center: ['50%', '65%'],
      startAngle: 195,
      endAngle: -15,
      min: 0,
      max: 100,
      z: 2,
      itemStyle: {
        color: '#FFF'
      },
      progress: { // 进度条
        show: true,
        width: 18,
        roundCap: true,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
            {
              offset: 0,
              color: 'rgba(37, 196, 248, 0)'
            },
            {
              offset: 1,
              color: 'rgba(37, 196, 248, 0.85)'
            }
          ])
        }
      },
      pointer: { // 指针
        icon: 'path://M2090.36389,615.30999 L2090.36389,615.30999 C2091.48372,615.30999 2092.40383,616.194028 2092.44859,617.312956 L2096.90698,728.755929 C2097.05155,732.369577 2094.2393,735.416212 2090.62566,735.56078 C2090.53845,735.564269 2090.45117,735.566014 2090.36389,735.566014 L2090.36389,735.566014 C2086.74736,735.566014 2083.81557,732.63423 2083.81557,729.017692 C2083.81557,728.930412 2083.81732,728.84314 2083.82081,728.755929 L2088.2792,617.312956 C2088.32396,616.194028 2089.24407,615.30999 2090.36389,615.30999 Z',
        length: '80%',
        width: 12,
        offsetCenter: [0, '10%']
      },
      axisLine: {
        show: false,
      },
      axisTick: { // 刻度
        distance: -27,
        splitNumber: 6,
        length: 14,
        lineStyle: {
          width: 3,
          color: 'rgba(37, 196, 248, 0.3)'
        }
      },
      splitLine: {
        show: false
      },
      axisLabel: {
        show: false
      },
      anchor: { // 指针上的小点
        show: true,
        showAbove: true,
        size: 6,
        itemStyle: {
          color: '#44FFFF'
        }
      },
      title: {
        show: false
      },
      detail: {
        valueAnimation: true,
        width: '60%',
        lineHeight: 40,
        borderRadius: 8,
        offsetCenter: [0, '67%'],
        fontSize: 12,
        fontWeight: 'bolder',
        formatter: `{small|${props.title}} {big|{value}%}`,
        rich: {
          big: {
            fontSize: 32,
            lineHeight: 40,
            fontWeight: 'bold',
            fontFamily: 'DS-digital',
          },
          small: {
            fontSize: 16,
            lineHeight: 40,
          }
        },
        color: 'inherit'
      },
      data: [
        {
          value: props.data
        }
      ]
    },
    {
      type: 'gauge',
      center: ['50%', '65%'],
      startAngle: 195,
      endAngle: -15,
      min: 0,
      max: 100,
      z: 3,
      itemStyle: {
        color: '#FFF'
      },
      progress: { show: false, },
      pointer: { // 指针
        icon: 'circle',
        length: '80%',
        width: 12,
        offsetCenter: [0, '-48.5%']
      },
      axisLine: { show: false, },
      axisTick: { show: false },
      splitLine: { show: false },
      axisLabel: { show: false },
      anchor: { show: false, },
      title: { show: false },
      detail: { show: false },
      data: [{ value: props.data }]
    },
  ],
  graphic: {
    elements: [
      {
        type: "image",
        style: {
          image: img,
          width: 27,
          height: 27,
        },
        left: 'center',
        top: '59%',
        z: 1
      },
    ]
  }
};


const gaugeChartRef = ref()
const chartInstance = ref();

// 绘制(更新)
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// 初始化
const initBar = () => {
  if (!gaugeChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(gaugeChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(gaugeChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
}

// resize
const resizeChartfun = debounce(() => {
  option.series[0].detail.rich.big.fontSize = offsetWidth.value / 1920 * 32
  option.series[0].detail.rich.big.lineHeight = offsetWidth.value / 1920 * 40
  option.series[0].detail.rich.small.fontSize = offsetWidth.value / 1920 * 16
  option.series[0].detail.rich.big.lineHeight = offsetWidth.value / 1920 * 40

  nextTick(() => {
    chartInstance.value.resize();
    draw()
  });
}, 500)

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option.series[0].data[0].value = props.data;
    option.series[1].data[0].value = props.data;
    draw()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  initBar()
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
})

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});
</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  @media screen and (max-width: 1960px) {
    font-size: 10px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 8px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 6px;
  }

  .gaugeChart {
    width: 100%;
    height: 100%;
  }
}
</style>
