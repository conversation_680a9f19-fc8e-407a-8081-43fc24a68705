import { defineStore } from 'pinia'
import {
  getCountGroupArea, getEmpImportantIndustry, getEmpIndustry, getDeclareTrend, getEmpEconomic, getEmpEnterpriseSize, getDeclareSituation,
  getFirstDeclareTrend, getEmpCountHazard
} from '@/api/report'

export const useReportStore = defineStore('report', {
  state: () => ({

    // 地区分布初始数据
    axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
    seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],

    mapData: [], // 地图数据 地区分布的申报数和申报企业数
    empTotal: 0, // 申报企业总数

    // 申报企业地区分布 左上 getters中
    // empArea: [],

    empImportantIndustryData: [], // 申报企业重点行业分布 左中

    empIndustry: [], // 申报企业行业分布 左下

    // 每月申报企业趋势 右上
    declareTrend: {
      axisData: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    },

    // 申报企业经济类型分布 右中
    empEconomic: {
      axisData: ['类型1', '类型2', '类型3', '类型4', '类型5'],
      seriesData: [0, 0, 0, 0, 0],
    },

    // 申报企业企业规模分布 右下
    empEnterpriseSize: {
      axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
      seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    },

    // 申报企业情况分布 中下
    declareSituation: {
      "detectionRate": 0, // 检测率
      "examinationRate": 0, // 体检率
      "leaderTrainRate": 0, // 法人培训率
      "managerTrainRate": 0, // 管理人员培训率
      "staffTrainRate": 0 // 劳动者培训率
    },

    // 初次申报地区分布趋势 左中
    firstDeclareTrend: {
      axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
      seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    },

    // 申报企业情况分布
    empCountHazard: {
      axisData: ['矽尘', '煤尘', '石棉粉尘', '滑石粉尘', '白炭黑粉尘', '铅及其化合物', '苯', '噪声'],
      seriesData1: [0, 0, 0, 0, 0, 0, 0, 0],
      seriesData2: [0, 0, 0, 0, 0, 0, 0, 0],
    }

  }),

  getters: {

    empArea(state) {
      let axisData = state.axisData
      let seriesData = state.seriesData
      if (state.mapData.length) {
        // const total = state.mapData.reduce((total, item) => total + item.empCount, 0)
        axisData = state.mapData.map(item => item.name.slice(0, 2))
        seriesData = state.mapData.map(item => item.empCount)
      }
      return {
        seriesData,
        axisData
      }
    },

  },

  actions: {

    // 地图数据 地区分布的申报数和申报企业数
    async getCountGroupAreaData(params?: { year?: string | null, code?: string }) {
      const res = await getCountGroupArea(params)
      this.mapData = res.data
      // 计算地图上方总数
      this.empTotal = this.mapData.reduce((total, item: declareCountGroupArea) => total + item.empCount, 0)
    },

    async getEmpImportantIndustryData(params?: { year?: string | null, code?: string }) {
      const res = await getEmpImportantIndustry(params)
      this.empImportantIndustryData = res.data
    },

    async getEmpIndustryData(params?: { year?: string | null, code?: string }) {
      const res = await getEmpIndustry(params)
      this.empIndustry = res.data
    },

    async getDeclareTrendData(params?: { year?: string | null, code?: string }) {
      const res = await getDeclareTrend(params)
      this.declareTrend.axisData = res.data.map((item: any) => item.name + '月')
      this.declareTrend.seriesData = res.data.map((item: any) => item.value)
    },

    async getEmpEconomicData(params?: { year?: string | null, code?: string }) {
      const res = await getEmpEconomic(params)
      const total = res.data.reduce((total: number, item: any) => total + item.value, 0)
      this.empEconomic.axisData = res.data.map((item: any) => item.name).slice(-5)
      this.empEconomic.seriesData = res.data.map((item: any) => (item.value / total * 100).toFixed(2)).slice(-5)
    },

    async getEmpEnterpriseSizeData(params?: { year?: string | null, code?: string }) {
      const res = await getEmpEnterpriseSize(params)
      this.empEnterpriseSize.axisData = res.data.map((item: any) => item.name)
      const total = res.data.reduce((total: number, item: any) => total + item.value, 0)
      this.empEnterpriseSize.seriesData = res.data.map((item: any) => (item.value / total * 100).toFixed(2))
    },

    async getDeclareSituationData(params?: { year?: string | null, code?: string }) {
      const res = await getDeclareSituation(params)
      this.declareSituation = res.data
    },

    async getFirstDeclareTrendData(params?: { year?: string | null, code?: string }) {
      const res = await getFirstDeclareTrend(params)
      this.firstDeclareTrend.axisData = res.data.map((item: any) => item.name.slice(0, 2))
      this.firstDeclareTrend.seriesData = res.data.map((item: any) => item.value)
    },

    async getEmpCountHazardData(params?: { year?: string | null, code?: string }) {
      const res = await getEmpCountHazard(params)
      this.empCountHazard.axisData = res.data.map((item: any) => item.name)
      this.empCountHazard.seriesData1 = res.data.map((item: any) => item.empCount || 0)
      this.empCountHazard.seriesData2 = res.data.map((item: any) => item.victimCount || 0)
    }

  }

})