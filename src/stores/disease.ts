import { defineStore } from 'pinia'
import {
  getCountGroupArea, getDeclareRateArea, getIndustryEmpCount, getReportCountArea,
  getOrgCountArea, getEmpCountHazard, getEmpCountSize,
  getCardTrendYear, getPointExceedIndustry, getPointExceedArea
} from '@/api/detection'

export const useDiseaseStore = defineStore('disease', {
  state: () => ({

    year: '2024年',

    code: '',

    mapData: [
      { name: "杭州市", value: 61 },
      { name: "宁波市", value: 58 },
      { name: "温州市", value: 22 },
      { name: "嘉兴市", value: 43 },
      { name: "湖州市", value: 49 },
      { name: "绍兴市", value: 23 },
      { name: "金华市", value: 19 },
      { name: "衢州市", value: 36 },
      { name: "舟山市", value: 3 },
      { name: "台州市", value: 123 },
      { name: "丽水市", value: 16 }
    ], // 地图数据
    // {
    //   name: string;
    //   value: number;
    //   data1: number;
    //   data2: number;
    //   data3: number;
    // }

    total: [
      { name: '检测企业数', value: 0 },
      { name: '超标点位数', value: 0 }
    ],


  }),

  getters: {

  },

  actions: {}

})