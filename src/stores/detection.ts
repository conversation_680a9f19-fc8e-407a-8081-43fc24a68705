import { defineStore } from 'pinia'
import {
  getCountGroupArea, getDeclareRateArea, getIndustryEmpCount, getReportCountArea,
  getOrgCountArea, getEmpCountHazard, getEmpCountSize,
  getCardTrendYear, getPointExceedIndustry, getPointExceedArea
} from '@/api/detection'

export const useDetectionStore = defineStore('detection', {
  state: () => ({

    mapData: [], // 地图数据 检测数、检测企业数
    // {
    //   name: string;
    //   value: number;
    //   declare: number; // 检测数
    //   empCount: number; //检测企业数
    // }

    total: [
      { name: '检测企业数', value: 0 },
      { name: '超标点位数', value: 0 }
    ],

    // 检测企业地区分布 左上
    empCountArea: {
      axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
      seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    },

    // 检测企业地区申报率 左中
    declareRateArea: {
      axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
      seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      data: []
    },

    // 检测企业行业分布 左下
    industryEmpCount: [
      { name: "杭州市", value: 0 },
      { name: "宁波市", value: 0 },
      { name: "温州市", value: 0 },
      { name: "嘉兴市", value: 0 },
      { name: "湖州市", value: 0 },
      { name: "绍兴市", value: 0 },
      { name: "金华市", value: 0 },
      { name: "衢州市", value: 0 },
      { name: "舟山市", value: 0 },
      { name: "台州市", value: 0 },
      { name: "丽水市", value: 0 },
    ],

    // 个案卡历年趋势 右上
    cardTrendYear: {
      axisData: ['2020', '2021', '2022', '2023', '2024'],
      seriesData: [0, 0, 0, 0, 0]
    },

    // 各行业检测点超标情况 右中
    pointExceedIndustry: [],

    // 各地区检测点超标情况 右下
    pointExceedArea: [],

    // 职业卫生技术服务机构分布 中下1
    orgCountArea: {
      axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
      seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    },

    // 检测危害因素分布 中下2
    empCountHazard: {
      axisData: ['苯', '铅', '噪声', '矽尘', '煤尘', '电焊烟尘'],
      seriesData: [0, 0, 0, 0, 0, 0],
    },

    // 检测企业规模分布 中下3
    empCountSize: [
      { value: 0, name: '微型' },
      { value: 0, name: '小' },
      { value: 0, name: '中' },
      { value: 0, name: '大' },
      { value: 0, name: '不详' }
    ]

  }),

  getters: {
    pointExceedIndustryData(state) {
      if (state.pointExceedIndustry.length === 0) {
        return { axisData: [], barData: [], lineData: [] }
      }
      const axisData = state.pointExceedIndustry.map((item: any) => item.industryName)
      const barData = state.pointExceedIndustry.map((item: any) => item.pointCount)
      const lineData = state.pointExceedIndustry.map((item: any) => item.pointCount > 0 ? (item.exceedPointCount / item.pointCount * 100).toFixed(2) : 0)
      return { axisData, barData, lineData }
    },

    pointExceedAreaData(state) {
      if (state.pointExceedArea.length === 0) {
        return { axisData: [], barData: [], lineData: [] }
      }
      const axisData = state.pointExceedArea.map((item: any) => item.areaName.slice(0, 2))
      const barData = state.pointExceedArea.map((item: any) => item.pointCount)
      const lineData = state.pointExceedArea.map((item: any) => item.pointCount > 0 ? (item.exceedPointCount / item.pointCount * 100).toFixed(2) : 0)
      return { axisData, barData, lineData }
    }
  },

  actions: {
    // 获取检测企业地区分布 左上 ；地区检测数据
    async getEmpCountAreaData(params?: { year?: string | null, code?: string }) {
      const { data } = await getCountGroupArea(params)
      this.empCountArea.axisData = data.map((item: resObj) => item.name.slice(0, 2))
      this.empCountArea.seriesData = data.map((item: resObj) => item.value || 0)
      this.mapData = data.map((item: resObj) => ({
        name: item.name,
        value: 0,
        declare: 0,
        empCount: item.value || 0
      }))
      this.total[0].value = this.mapData.reduce((total: number, item: resObj) => total + item.empCount, 0)
      // 地区检测数据
      const res = await getReportCountArea(params)
      for (let i = 0; i < this.mapData.length; i++) {
        this.mapData[i].declare = res.data[i].value
      }
    },

    // 获取检测企业地区申报率 左中
    async getDeclareRateAreaData(params?: { year?: string | null, code?: string }) {
      const { data } = await getDeclareRateArea(params)
      console.log('getDeclareRateArea data', data)
      this.declareRateArea.data = data
      this.declareRateArea.axisData = data.map((item: resObj) => item.areaName.slice(0, 2))
      this.declareRateArea.seriesData = data.map((item: resObj) => +item.detectionEmpCount > 0 ? (+item.declareEmpCount / +item.detectionEmpCount * 100).toFixed(2) : 0)
      console.log('this.declareRateArea.seriesData', this.declareRateArea.seriesData)
    },

    // 获取检测企业行业分布 左下
    async getIndustryEmpCountData(params?: { year?: string | null, code?: string }) {
      const { data } = await getIndustryEmpCount(params)
      this.industryEmpCount = data
    },

    // 获取个案卡历年趋势 右上
    async getCardTrendYearData(params?: { year?: string | null, code?: string }) {
      const { data } = await getCardTrendYear(params)
      this.cardTrendYear.axisData = data.map((item: resObj) => item.year)
      this.cardTrendYear.seriesData = data.map((item: resObj) => item.reportCount)
    },

    // 获取各行业检测点超标情况 右中
    async getPointExceedIndustryData(params?: { year?: string | null, code?: string }) {
      const { data } = await getPointExceedIndustry(params)
      this.pointExceedIndustry = data
    },

    // 获取各地区检测点超标情况 右下
    async getPointExceedAreaData(params?: { year?: string | null, code?: string }) {
      const { data } = await getPointExceedArea(params)
      this.pointExceedArea = data
      this.total[1].value = this.pointExceedArea.reduce((total: number, item: resObj) => total + item.exceedPointCount, 0)
    },

    // 获取职业卫生技术服务机构分布 中下1
    async getOrgCountAreaData(params?: { year?: string | null, code?: string }) {
      const { data } = await getOrgCountArea(params)
      this.orgCountArea.axisData = data.map((item: resObj) => item.name.slice(0, 2))
      this.orgCountArea.seriesData = data.map((item: resObj) => item.value)
    },

    // 检测危害因素分布 中下2
    async getEmpCountHazardData(params?: { year?: string | null, code?: string }) {
      const { data } = await getEmpCountHazard(params)
      this.empCountHazard.axisData = data.map((item: resObj) => item.name)
      this.empCountHazard.seriesData = data.map((item: resObj) => item.value)
    },

    // 获取检测企业规模分布 中下3
    async getEmpCountSizeData(params?: { year?: string | null, code?: string }) {
      const { data } = await getEmpCountSize(params)
      const total = data.reduce((total: number, item: resObj) => total + item.value, 0)
      this.empCountSize = data.map((item: resObj) => ({
        name: item.name,
        value: total > 0 ? +(item.value / total * 100).toFixed(2) : 0 // 必须为number类型
      }))
    }

  }

})