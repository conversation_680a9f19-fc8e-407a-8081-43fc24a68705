// 首页

import { defineStore } from 'pinia'
import {
  getSummaryCounts, getInstitution, getNewOccupationalDiseases, getRehabilitationStation, getDeclaredEmployer,
  getDeclareCountGroupArea, getDetectionCountGroupArea, getAreaCardCountGroup
} from '@/api/home'



export const useHomeStore = defineStore('home', {
  state: () => ({
    // 地区分布初始数据
    axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
    seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],

    // 地图 
    summaryCounts: [] as mapAreaData[],

    declaredEmployerTotal: 0, // 申报企业总数
    checkedEmployerTotal: 0,  // 检测企业总数
    examCardTotal: 0,        // 体检个案总数

    // 地图下 机构统计
    orgInstitution: [
      { name: '职业卫生技术服务机构数', value: 0 },
      { name: '职业健康检查机构数', value: 0 },
      { name: '职业病鉴定机构数', value: 0 },
      { name: '放射卫生技术服务机构数', value: 0 },
      { name: '职业病诊断机构数', value: 0 },
      { name: '尘肺病康复站数', value: 0 },
    ],

    // 左上 申报企业地区分布 gettters中
    // DeclaredEmployer

    // 左中 最新申报企业
    latestDeclare: [
      // { region: '温州市/平阳县1', employer: '公司名称公司名称公司名称公司名称', date: '2024-24-24' },
    ],

    // 左下 检测企业地区分布 gettters中
    // CheckedEmployer

    // 右上 体检个案地区分布 gettters中
    // ExamCard

    // 右中 新发职业病地区分布
    NewOccupationalDiseases: [
      { value: 0, name: '杭州' },
      { value: 0, name: '宁波' },
      { value: 0, name: '温州' },
      { value: 0, name: '嘉兴' },
      { value: 0, name: '湖州' },
      { value: 0, name: '绍兴' },
      { value: 0, name: '金华' },
      { value: 0, name: '衢州' },
      { value: 0, name: '舟山' },
      { value: 0, name: '台州' },
      { value: 0, name: '丽水' }
    ],

    // 右下 尘肺病康复站地区分布
    RehabilitationStation: {
      axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
      seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    },
  }),
  getters: {
    // 左上 申报企业地区分布 gettters
    DeclaredEmployer: (state) => {
      let axisData: string[] = state.axisData
      let seriesData: number[] = state.seriesData
      if (state.summaryCounts.length) {
        axisData = state.summaryCounts.map((item: any) => item.name.slice(0, 2))
        seriesData = state.summaryCounts.map((item: any) => item.declaredEmployerCount)
      }
      return {
        axisData,
        seriesData
      }
    },

    // 左下 检测企业地区分布
    CheckedEmployer: (state) => {
      let seriesData = [
        { value: 0, name: '杭州' },
        { value: 0, name: '宁波' },
        { value: 0, name: '温州' },
        { value: 0, name: '嘉兴' },
        { value: 0, name: '湖州' },
        { value: 0, name: '绍兴' },
        { value: 0, name: '金华' },
        { value: 0, name: '衢州' },
        { value: 0, name: '舟山' },
        { value: 0, name: '台州' },
        { value: 0, name: '丽水' }
      ]
      if (state.summaryCounts.length) {
        seriesData = state.summaryCounts.map((item: any) => ({
          name: item.name.slice(0, 2),
          value: item.checkedEmployerCount
        }))
      }
      return {
        seriesData
      }
    },

    // 右上 体检个案地区分布
    ExamCard: (state) => {
      let axisData: string[] = state.axisData
      let seriesData: number[] = state.seriesData
      if (state.summaryCounts.length) {
        axisData = state.summaryCounts.map((item: any) => item.name.slice(0, 2))
        seriesData = state.summaryCounts.map((item: any) => item.examCardCount)
      }
      return {
        axisData,
        seriesData
      }
    },

    getOrgInstitution: (state) => {
      return state.orgInstitution
    },
  },

  actions: {
    // 获取地图数据
    // async getSummaryCountsData(params?: { year?: string | null, code?: string }) {
    //   const res = await getSummaryCounts(params)
    //   this.summaryCounts = res.data
    //   // 计算地图上方总数
    //   this.declaredEmployerTotal = this.summaryCounts.reduce((total, item) => total + item.declaredEmployerCount, 0)
    //   this.checkedEmployerTotal = this.summaryCounts.reduce((total, item) => total + item.checkedEmployerCount, 0)
    //   this.examCardTotal = this.summaryCounts.reduce((total, item) => total + item.examCardCount, 0)
    // },

    // 获取地图数据
    async getSummaryCountsData(params?: { year?: string | null, code?: string }) {
      const declaredEmployerRes = await getDeclareCountGroupArea(params) // 申报企业数和申报数地区分布
      this.declaredEmployerTotal = declaredEmployerRes.data.reduce((total, item) => total + item.empCount, 0)
      this.summaryCounts = declaredEmployerRes.data.map((item: any) => ({
        name: item.name,
        code: '',
        value: 0,
        declaredEmployerCount: item.empCount || 0, // 申报企业数
        checkedEmployerCount: 0, // 检测企业数
        examCardCount: 0 // 体检个案数
      }))
      const checkedEmployerRes = await getDetectionCountGroupArea(params) // 检测企业地区分布
      this.checkedEmployerTotal = checkedEmployerRes.data.reduce((total, item) => total + item.value, 0)
      this.summaryCounts.forEach((item, index) => {
        const data = checkedEmployerRes.data.find((i: any) => i.name === item.name)
        if (data) {
          item.checkedEmployerCount = data.value || 0
          item.code = data.code
        }
      })
      const examCardRes = await getAreaCardCountGroup(params) //体检个案数地区分布
      this.examCardTotal = examCardRes.data.reduce((total, item) => total + item.value, 0)
      this.summaryCounts.forEach((item, index) => {
        const data = examCardRes.data.find((i: any) => i.name === item.name)
        if (data) {
          item.examCardCount = data.value || 0
        }
      })
      // 计算地图上方总数
      this.checkedEmployerTotal = this.summaryCounts.reduce((total, item) => total + item.checkedEmployerCount, 0)
      this.examCardTotal = this.summaryCounts.reduce((total, item) => total + item.examCardCount, 0)
    },

    // 获取机构统计数据
    async getOrgInstitutionData(params?: { code?: string }) {
      const res = await getInstitution(params)
      if (res.data.length === 0) {
        return
      } else {
        this.orgInstitution = res.data
        // res.data.forEach((item: any) => {
        //   const findItem = this.orgInstitution.find((org: any) => org.name === item.name)
        //   if (findItem) {
        //     findItem.value = item.value
        //   }
        // })
      }
    },

    // 新发职业病地区分布
    async getNewOccupationalDiseasesData(params: { year: string | null, code?: string }) {
      const res = await getNewOccupationalDiseases(params)
      // this.NewOccupationalDiseases = res.data
      if (res.data.length) {
        this.NewOccupationalDiseases = res.data.map((item: any) => ({
          name: item.name.slice(0, 2),
          value: item.value
        }))
      }
    },

    // 获取康复站数据
    async getRehabilitationStationData(params?: { code?: string }) {
      const res = await getRehabilitationStation(params)
      this.RehabilitationStation.axisData = res.data.map((item: any) => item.name.slice(0, 2))
      this.RehabilitationStation.seriesData = res.data.map((item: any) => item.value)
    },

    // 获取最新申报企业
    async getDeclaredEmployer(params?: { year?: string | null; code?: string }) {
      const res = await getDeclaredEmployer(params)
      this.latestDeclare = res.data
    }
  }
});
