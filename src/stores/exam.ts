import { defineStore } from 'pinia'
import {
  getAreaCardCountGroup, getEmpGroupArea,
  getReportRate, getIndustryEmpCount,
  getExamOrgArea, getHazardCardCount, getIndustryEnterpriseSize,
  getCardCountYearTrend, getIndustryCardDiagnosis, getAreaCardDiagnosis
} from '@/api/exam'


export const useExamStore = defineStore('exam', {
  state: () => ({

    mapData: [
      // {
      //   name: string;
      //   value: number;
      //   examCard: number;
      //   empGroup: number;
      // }
    ], // 地图数据 体检(个案)数、体检企业数

    total: [
      { name: '体检个案数', value: 0 },
      { name: '体检企业数', value: 0 },
      { name: '职业健康检查机构数', value: 0 }
    ],

    // 体检企业地区分布
    empGroupArea: [],

    // 体检个案数地区分布 左上
    areaCard: [
      { name: "杭州市", value: 0 },
      { name: "宁波市", value: 0 },
      { name: "温州市", value: 0 },
      { name: "嘉兴市", value: 0 },
      { name: "湖州市", value: 0 },
      { name: "绍兴市", value: 0 },
      { name: "金华市", value: 0 },
      { name: "衢州市", value: 0 },
      { name: "舟山市", value: 0 },
      { name: "台州市", value: 0 },
      { name: "丽水市", value: 0 }
    ],

    // 报告率 左中
    reportRate: [
      { name: '浙江省', rate: 0, examCount: 0, cardCount: 100 },
      { name: '杭州', rate: 0, examCount: 0, cardCount: 100 },
      { name: '宁波', rate: 0, examCount: 0, cardCount: 100 },
      { name: '温州', rate: 0, examCount: 0, cardCount: 100 },
      { name: '嘉兴', rate: 0, examCount: 0, cardCount: 100 },
      { name: '湖州', rate: 0, examCount: 0, cardCount: 100 },
      { name: '绍兴', rate: 0, examCount: 0, cardCount: 100 },
      { name: '金华', rate: 0, examCount: 0, cardCount: 100 },
      { name: '衢州', rate: 0, examCount: 0, cardCount: 100 },
      { name: '舟山', rate: 0, examCount: 0, cardCount: 100 },
      { name: '台州', rate: 0, examCount: 0, cardCount: 100 },
      { name: '丽水', rate: 0, examCount: 0, cardCount: 100 },
    ],

    // 体检企业行业分布 左下
    industryEmpCount: {
      axisData: ['重点行业', '纺织行业', '制造业', '采矿', '手工'],
      seriesData: [0, 0, 0, 0, 0],
    },

    // 职业健康检查机构分布 中下1
    examOrgArea: {
      axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
      seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    },

    // 体检危害因素分布 中下2
    hazardCardCount: {
      axisData: ['苯', '铅', '噪声', '矽尘', '煤尘', '电焊烟尘'],
      seriesData: [0, 0, 0, 0, 0, 0],
    },

    // 体检企业规模分布 中下3
    IndustryEnterpriseSize: [
      { value: 0, name: '微型' },
      { value: 0, name: '小' },
      { value: 0, name: '中' },
      { value: 0, name: '大' },
      { value: 0, name: '不详' }
    ],

    // 体检个案历年趋势 右上
    cardCountYearTrend: [{
      "year": "2024",
      "cardCount": 0,
      "suspectedCount": 0,
      "contraindicationCount": 0,
    },],

    // 个案卡行业疑似和禁忌证检出情况 右中
    industryCardDiagnosis: {
      axisData: ['重点行业', '纺织行业', '制造业', '采矿', '手工'],
      total: [0, 0, 0, 0, 0], // 体检个案
      data1: [0, 0, 0, 0, 0], // 疑似
      data2: [0, 0, 0, 0, 0], // 禁忌
    },
    // [{
    //   "industry": "行业类别",
    //   "cardCount": 0,
    //   "suspectedCount": 0,
    //   "contraindicationCount": 0
    // }],

    // 个案卡地区疑似禁忌检出情况 右下
    areaCardDiagnosis: {
      axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
      total: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 体检个案
      data1: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 疑似
      data2: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], // 禁忌
    },
    // [{
    //   "areaCode": "3301",
    //   "areaName": "杭州市",
    //   "cardCount": 0,
    //   "suspectedCount": 0,
    //   "contraindicationCount": 0
    // }]

  }),

  getters: {},

  actions: {
    // 体检个案数地区分布 左上 体检企业数地区分布(地图)
    async getAreaCardCountGroup(params?: { year?: string | null, code?: string }) {
      const { data } = await getAreaCardCountGroup(params)
      this.areaCard = data.map((item: resObj) => ({
        ...item,
        name: item.name.slice(0, 2)
      }))
      this.total[0].value = this.areaCard.reduce((total: number, item: resObj) => total + item.value, 0)
      this.mapData = data.map((item: resObj) => ({
        name: item.name,
        value: item.value,
        examCard: item.value,
        empGroup: 0
      }))

      const EmpGroupRes = await getEmpGroupArea(params)
      for (let i = 0; i < this.mapData.length; i++) {
        this.mapData[i].empGroup = EmpGroupRes.data[i].value
      }
      this.total[1].value = this.mapData.reduce((total: number, item: resObj) => total + item.empGroup, 0)
    },

    // 报告率 左中
    async getReportRate(params?: { year?: string | null, code?: string }) {
      const { data } = await getReportRate(params)
      this.reportRate = data.map((item: resObj, i: number) => ({
        name: i > 0 ? item.name.slice(0, 2) : item.name,
        rate: item.examCount > 0 ? (item.cardCount / item.examCount * 100).toFixed(2) : 0,
        examCount: item.examCount,
        cardCount: item.cardCount,
      }))
    },

    // 体检企业行业分布 左下
    async getIndustryEmpCountData(params?: { year?: string | null, code?: string }) {
      let { data } = await getIndustryEmpCount(params)
      this.industryEmpCount.axisData = data.map((item: resObj) => item.name)
      this.industryEmpCount.seriesData = data.map((item: resObj) => item.value)
    },

    // 职业健康检查机构分布 中下1
    async getExamOrgAreaData(params?: { year?: string | null, code?: string }) {
      const { data } = await getExamOrgArea(params)
      this.examOrgArea.axisData = data.map((item: resObj) => item.name.slice(0, 2))
      this.examOrgArea.seriesData = data.map((item: resObj) => item.value)
      this.total[2].value = this.examOrgArea.seriesData.reduce((total: number, item: number) => total + item, 0)
    },

    // 体检危害因素分布 中下2
    async getHazardCardCount(params?: { year?: string | null, code?: string }) {
      const { data } = await getHazardCardCount(params)
      const total = data.reduce((total: number, item: resObj) => total + item.value, 0)
      this.hazardCardCount.axisData = data.map((item: resObj) => item.name)
      this.hazardCardCount.seriesData = data.map((item: resObj) => (item.value / total * 100).toFixed(2))
    },

    // 体检企业规模分布 中下3
    async getIndustryEnterpriseSizeData(params?: { year?: string | null, code?: string }) {
      const { data } = await getIndustryEnterpriseSize(params)
      const total = data.reduce((total: number, item: resObj) => total + item.value, 0)
      this.IndustryEnterpriseSize = data.map((item: resObj) => ({
        name: item.name,
        value: +(item.value / total * 100).toFixed(2) // 必须为number类型
      }))
    },

    // 体检个案历年趋势 右上
    async getCradCountYearTrendData(params?: { year?: string | null, code?: string }) {
      const { data } = await getCardCountYearTrend(params)
      this.cardCountYearTrend = data
    },

    // 个案卡行业疑似和禁忌检出情况 右中
    async getIndustryCardDiagnosisData(params?: { year?: string | null, code?: string, orderBy?: string }) {
      const { data } = await getIndustryCardDiagnosis(params)
      // cardCount
      // suspectedCount
      // contraindicationCount
      this.industryCardDiagnosis.axisData = data.map((item: resObj) => item.industry)
      this.industryCardDiagnosis.total = data.map((item: resObj) => item.cardCount)
      this.industryCardDiagnosis.data1 = data.map((item: resObj) => item.suspectedCount)
      this.industryCardDiagnosis.data2 = data.map((item: resObj) => item.contraindicationCount)
    },

    // 个案卡地区疑似禁忌检出情况 右下
    async getAreaCardDiagnosisData(params?: { year?: string | null, code?: string }) {
      const { data } = await getAreaCardDiagnosis(params)
      this.areaCardDiagnosis.axisData = data.map((item: resObj) => item.areaName.slice(0, 2))
      this.areaCardDiagnosis.total = data.map((item: resObj) => item.cardCount)
      this.areaCardDiagnosis.data1 = data.map((item: resObj) => item.suspectedCount)
      this.areaCardDiagnosis.data2 = data.map((item: resObj) => item.contraindicationCount)
    },

  },

})