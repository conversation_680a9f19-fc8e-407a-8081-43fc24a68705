// 个人剂量
import { defineStore } from "pinia";
import {
  getDose_summaryemployees_great,
  getPersonSummaryCounts,
  getPersonemployee_occupation,
  getInstitutions,
  getexchange_ratio,
} from "@/api/personalDose";

export const usePersonalDoseStore = defineStore("personalDose", {
  state: () => ({
    // 初始数据
    axisData: [
      "杭州",
      "宁波",
      "温州",
      "嘉兴",
      "湖州",
      "绍兴",
      "金华",
      "衢州",
      "舟山",
      "台州",
      "丽水",
    ],
    seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    // 中下 机构统计
    // institution: [
    //   { name: "技术服务机构数", value: 0 },
    //   { name: "检测仪器数", value: 0 },
    //   { name: "技术服务人员数", value: 0 },
    //   { name: "监测报告总数", value: 0 },
    //   { name: "异常监测报告数", value: 0 },
    //   { name: "不合格报告数", value: 0 },
    // ],

    // 右中 年受照剂量超5mSv人数地区分布
    annualRegionalDistributionDoses: [],

    // 右下 季度受照剂量超1.25mSv变化趋势图
    quarterlyExposureDose: {
      xAxisData: ["2021Q1", "2021Q2", "2021Q3", "2021Q4"],
      seriesData: [0, 0, 0, 0],
    },

    // 左下 监测结果超1.25mSv人次数
    testingResult: [],
    personSummaryCounts: [] as any[],
    persondEmployerTotal: 0, //受检单位数
    persondEmployeeTotal: 0, //放射工作人员监测总人数
    categoryResult: [] as any[], //右下 按照职业类别聚合放射人员
    listData: [],
    institutionalStatistics: {
      institutions: 0,
      instruments: 0,
      employees: 0,
      reports: 0,
      overDose: 0,
      doubleDoseRatio: 0,
    },
    //中下 机构统计
    ratioData: [] as any[], //省级监督系统
  }),

  getters: {
    // 省级监督系统
    RatioCollective: (state) => {
      let exchangeRatio: string = state.ratioData[0]
        ? state.ratioData[0]?.exchangeRatio * 100 + "%"
        : "0%";
      let successRatio: string = state.ratioData[0]
        ? state.ratioData[0]?.successRatio * 100 + "%"
        : "0%";
      return {
        exchangeRatio,
        successRatio,
      };
    },
    // 左上 年集体
    AnnualCollective: (state) => {
      let axisData: string[] = state.axisData;
      let seriesData: number[] = state.seriesData;
      if (state.personSummaryCounts.length) {
        axisData = state.personSummaryCounts.map((item: any) =>
          item.name.slice(0, 2)
        );
        seriesData = state.personSummaryCounts.map((item: any) =>
          parseFloat(item.doseSum.toFixed(3))
        );
      }
      return {
        axisData,
        seriesData,
      };
    },
    // 左中 平均剂量
    MeanCollective: (state) => {
      let seriesData = [
        { name: "杭州", value: 0 },
        { name: "宁波", value: 0 },
        { name: "温州", value: 0 },
        { name: "嘉兴", value: 0 },
        { name: "湖州", value: 0 },
        { name: "绍兴", value: 0 },
        { name: "金华", value: 0 },
        { name: "衢州", value: 0 },
        { name: "舟山", value: 0 },
        { name: "台州", value: 0 },
        { name: "丽水", value: 0 },
      ];
      if (state.personSummaryCounts.length) {
        seriesData = state.personSummaryCounts.map((item: any) => ({
          name: item.name.slice(0, 2),
          value: parseFloat(item.doseAvg.toFixed(3)),
        }));
      }
      return {
        seriesData,
      };
    },
    //  左下 监测结果超1.25mSv人次数s
    PersonalEmployer: (state) => {
      let axisData: string[] = state.axisData;
      let seriesData: number[] = state.seriesData;
      if (state.testingResult.length) {
        axisData = state.testingResult.map((item: any) => item.year);
        seriesData = state.testingResult.map((item: any) => item.cnt);
      }
      return {
        axisData,
        seriesData,
      };
    },
    // 右上 受检单位数地区分布
    personalExamCard: (state) => {
      let axisData: string[] = state.axisData;
      let seriesData: number[] = state.seriesData;
      if (state.personSummaryCounts.length) {
        axisData = state.personSummaryCounts.map((item: any) =>
          item.name.slice(0, 2)
        );
        seriesData = state.personSummaryCounts.map(
          (item: any) => item.employerCount
        );
      }
      return {
        axisData,
        seriesData,
      };
    },
    // 右中 监测中人员地区分布
    personalCheckedEmployer: (state) => {
      let seriesData = [
        { value: 0, name: "杭州" },
        { value: 0, name: "宁波" },
        { value: 0, name: "温州" },
        { value: 0, name: "嘉兴" },
        { value: 0, name: "湖州" },
        { value: 0, name: "绍兴" },
        { value: 0, name: "金华" },
        { value: 0, name: "衢州" },
        { value: 0, name: "舟山" },
        { value: 0, name: "台州" },
        { value: 0, name: "丽水" },
      ];
      if (state.personSummaryCounts.length > 0) {
        seriesData = state.personSummaryCounts.map((item: any) => ({
          name: item.name.slice(0, 2),
          value: item.employeeCount,
        }));
      }
      return {
        seriesData,
      };
    },
    //  右下 按照职业类别人数统计数据
    categoryResultcompu: (state) => {
      let listData = state.categoryResult;
      listData.forEach((item: any) => {
        item.region = item.occupation;
        item.date = item.cnt;
      });

      return {
        listData,
      };
    },
    // 中下 机构统计
    personalInstitutions: (state) => {
      let seriesData = [
        {
          name: "技术服务机构数",
          value: state.institutionalStatistics.institutions,
        },
        {
          name: "检测仪器数",
          value: state.institutionalStatistics.instruments,
        },
        {
          name: "技术服务人员数",
          value: state.institutionalStatistics.employees,
        },
        {
          name: "检测报告总数",
          value: state.institutionalStatistics.reports,
        },
        {
          name: "异常剂量调查数",
          value: state.institutionalStatistics.overDose,
        },
        {
          name: "双剂量监测率",
          value: `${state.institutionalStatistics.doubleDoseRatio}%`,
        },
      ];
      return {
        seriesData,
      };
    },
  },

  actions: {
    // 省级监督系统、交换率，成功率
    async getexchange_ratioData(params?: {
      year?: string | null;
      code?: string | null;
    }) {
      const res = await getexchange_ratio(params);
      this.ratioData = res.data;
    },
    // 获取地图数据 受检单位地区分布，监测中人员数地区分布 ,集体剂量,平均剂量
    async getPersonSummaryCountsData(params?: {
      year?: string | null;
      code?: string;
    }) {
      const res = await getPersonSummaryCounts(params);
      this.personSummaryCounts = res.data;
      // 计算地图上方总数
      // 受检单位数总数
      this.persondEmployerTotal = this.personSummaryCounts.reduce(
        (total, item) => total + item.employerCount,
        0
      );
      // 放射工作人员监测总人数
      this.persondEmployeeTotal = this.personSummaryCounts.reduce(
        (total, item) => total + item.employeeCount,
        0
      );
    },

    // 左下 监测结果超1.25mSv人次数
    async getDose_summaryemployees_great(params?: {
      year?: string | null;
      code?: string;
    }) {
      const res = await getDose_summaryemployees_great(params);
      this.testingResult = res.data;
    },
    // 右下 按照职业类别人数统计数据

    async personemployee_occupation(params?: { code?: string }) {
      const res = await getPersonemployee_occupation(params);
      this.categoryResult = res.data;
    },
    // 中下 机构统计
    async institutions(params?: { year?: string | null; code?: string }) {
      const res = await getInstitutions(params);
      this.institutionalStatistics = res.data;
    },
  },
});
