// 基本配置、公共数据
import { defineStore } from 'pinia'
import { getSummaryCounts, getInstitution, getNewOccupationalDiseases, getRehabilitationStation, getDeclaredEmployer } from '@/api/home'
import router from '@/router';

export const useDashBoardStore = defineStore('index', {
  // pinia
  // import { storeToRefs } from 'pinia';
  // import { useDashBoardStore } from '@/stores';
  // const dashBoardStore = useDashBoardStore();
  // const { offsetHeight } = storeToRefs(dashBoardStore)

  state: () => ({

    // token 不直接操作，通过setToken和resetToken操作，方便追踪；getToken获取
    token: '',

    // 当前视口宽高
    offsetWidth: 0,
    offsetHeight: 0,
    // 设计稿标准宽高
    standardWidth: 1920,
    standardHeight: 1080,
    // 标准宽高比
    standardRatio: 16 / 9,


    // 图表中主要颜色，常态、高亮
    mainColor: ['#18CACA', '#FFD33C'],
    // 字体颜色
    fontColor: ['#FFFFFF', '#18CACA'],
    // 图表颜色
    chartColor: [
      "rgba(250, 200, 88, 1)",
      "rgba(147, 190, 255, 1)",
      "rgba(80, 122, 252, 1)",
      "rgba(40, 62, 129, 1)",
      "rgba(145, 204, 117, 1)",
      "rgba(9, 124, 56, 1)",
      "rgba(104, 164, 0, 1)",
      "rgba(23, 227, 227, 1)",
      "rgba(3, 88, 105, 1)",
      "rgba(3, 127, 152, 1)",
      "rgba(3, 51, 59, 1)"
    ],

    fontSizeMini: 6,
    fontSizeSmall: 8,
    fontSizeNormal: 10,
    fontSizeLarge: 12,

    splitLineColor: '#333',

    resizeDebounceTime: 333,

    /* year 和 code 是上级数据，只有map组件会使用或操作，其余组件直接使用包含具体数据的下级数据，无需关注year和code的变化 */
    // 年份
    year: null, // null：历年 string：具体年份
    // 当前区域行政编码
    code: '', // '':浙江省 | '33**' 具体市区

    isFullScreen: false, // 是否全屏

    // 地区分布初始数据
    axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
    seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],

    /* 首页 */
    // 地图 
    summaryCounts: [] as mapAreaData[],

    declaredEmployerTotal: 0, // 申报企业总数
    checkedEmployerTotal: 0,  // 检测企业总数
    examCardTotal: 0,        // 体检个案总数

    // 地图下 机构统计
    orgInstitution: [
      { name: '职业卫生技术服务机构数', value: 0 },
      { name: '职业健康检查机构数', value: 0 },
      { name: '职业病鉴定机构数', value: 0 },
      { name: '放射卫生技术服务机构数', value: 0 },
      { name: '职业病诊断机构数', value: 0 },
      { name: '尘肺病康复站数', value: 0 },
    ],

    // 左上 申报企业地区分布 gettters中
    // DeclaredEmployer

    // 左中 最新申报企业
    latestDeclare: [
    ],

    // 左下 检测企业地区分布 gettters中
    // CheckedEmployer

    // 右上 体检个案地区分布 gettters中
    // ExamCard

    // 右中 新发职业病地区分布
    NewOccupationalDiseases: [
      { value: 0, name: '杭州' },
      { value: 0, name: '宁波' },
      { value: 0, name: '温州' },
      { value: 0, name: '嘉兴' },
      { value: 0, name: '湖州' },
      { value: 0, name: '绍兴' },
      { value: 0, name: '金华' },
      { value: 0, name: '衢州' },
      { value: 0, name: '舟山' },
      { value: 0, name: '台州' },
      { value: 0, name: '丽水' }
    ],

    // 右下 尘肺病康复站地区分布
    RehabilitationStation: {
      axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
      seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
    },
  }),
  getters: {
    getToken: (state) => state.token,

    // 左上 申报企业地区分布 gettters
    DeclaredEmployer: (state) => {
      let axisData: string[] = state.axisData
      let seriesData: number[] = state.seriesData
      if (state.summaryCounts.length) {
        axisData = state.summaryCounts.map((item: any) => item.name.slice(0, 2))
        seriesData = state.summaryCounts.map((item: any) => item.declaredEmployerCount)
      }
      return {
        axisData,
        seriesData
      }
    },

    // 左下 检测企业地区分布
    CheckedEmployer: (state) => {
      let seriesData = [
        { value: 0, name: '杭州' },
        { value: 0, name: '宁波' },
        { value: 0, name: '温州' },
        { value: 0, name: '嘉兴' },
        { value: 0, name: '湖州' },
        { value: 0, name: '绍兴' },
        { value: 0, name: '金华' },
        { value: 0, name: '衢州' },
        { value: 0, name: '舟山' },
        { value: 0, name: '台州' },
        { value: 0, name: '丽水' }
      ]
      if (state.summaryCounts.length) {
        seriesData = state.summaryCounts.map((item: any) => ({
          name: item.name.slice(0, 2),
          value: item.checkedEmployerCount
        }))
      }
      return {
        seriesData
      }
    },

    // 右上 体检个案地区分布
    ExamCard: (state) => {
      let axisData: string[] = state.axisData
      let seriesData: number[] = state.seriesData
      if (state.summaryCounts.length) {
        axisData = state.summaryCounts.map((item: any) => item.name.slice(0, 2))
        seriesData = state.summaryCounts.map((item: any) => item.examCardCount)
      }
      return {
        axisData,
        seriesData
      }
    },
  },

  actions: {
    // 设置当前视口宽高
    setOffsetWH(width: number, height: number) {
      this.offsetWidth = width
      this.offsetHeight = height

      // 重新计算字体大小
      this.fontSizeMini = height / this.standardHeight * 6
      this.fontSizeSmall = height / this.standardHeight * 8
      this.fontSizeNormal = height / this.standardHeight * 12
      this.fontSizeLarge = height / this.standardHeight * 12
    },

    // set
    setIsFullScreen(val: boolean) {
      this.isFullScreen = val
    },

    resetYearAndCode() {
      this.year = null
      this.code = ''
    },

    // 获取地图数据
    async getSummaryCountsData(params?: { year?: string | null, code?: string }) {
      const res = await getSummaryCounts(params)
      this.summaryCounts = res.data
      // 计算地图上方总数
      this.declaredEmployerTotal = this.summaryCounts.reduce((total, item) => total + item.declaredEmployerCount, 0)
      this.checkedEmployerTotal = this.summaryCounts.reduce((total, item) => total + item.checkedEmployerCount, 0)
      this.examCardTotal = this.summaryCounts.reduce((total, item) => total + item.examCardCount, 0)
    },

    // 获取机构统计数据
    async getOrgInstitutionData(params?: { code?: string }) {
      const res = await getInstitution(params)
      if (res.data.length === 0) {
        return
      } else {
        this.orgInstitution = res.data
        // res.data.forEach((item: any) => {
        //   const findItem = this.orgInstitution.find((org: any) => org.name === item.name)
        //   if (findItem) {
        //     findItem.value = item.value
        //   }
        // })
      }
    },

    // 新发职业病地区分布
    async getNewOccupationalDiseasesData(params: { year: string | null, code?: string }) {
      const res = await getNewOccupationalDiseases(params)
      // this.NewOccupationalDiseases = res.data
      if (res.data.length) {
        this.NewOccupationalDiseases = res.data.map((item: any) => ({
          name: item.name.slice(0, 2),
          value: item.value
        }))
      }
    },

    // 获取康复站数据
    async getRehabilitationStationData(params?: { code?: string }) {
      const res = await getRehabilitationStation(params)
      this.RehabilitationStation.axisData = res.data.map((item: any) => item.name.slice(0, 2))
      this.RehabilitationStation.seriesData = res.data.map((item: any) => item.value)
    },

    // 获取最新申报企业
    async getDeclaredEmployer(params?: { year?: string | null; code?: string }) {
      const res = await getDeclaredEmployer(params)
      this.latestDeclare = res.data
    },

    setToken(token: string) {
      this.token = token
    },
    resetToken() {
      this.token = ''
    },
    logout(redirectUrl: string = '/uncertified') {
      this.resetToken()
      router.replace(redirectUrl)
    },
  },
  // 为token添加持久化
  persist: [{
    pick: ['token'],
    storage: localStorage,
  }]
});
