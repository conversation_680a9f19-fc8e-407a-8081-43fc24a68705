// 个人剂量
import { defineStore } from 'pinia'
import {
  getInstitutionRegion, getDeviceRegion,
  getReportedCustomerOrgRegion, getRadiationDeviceCategory, getRadiationEmployerRegion,
  getRadiationProjectType, getUnqualifiedDeviceRegion, getRadiationEmployerType,
  getRadiationInstitutions, getRadiationReports
} from '@/api/medicalRadiation'

export const useMedicalRadiationStore = defineStore('medicalRadiation', {
  state: () => ({

    // 地图数据 受检单位数、检测机构数、设备数
    mapData: [
      // {
      //   name: string;
      //   value: number; // visualMap
      //   employer: number; // 受检单位数
      //   institution: number; // 检测机构数
      //   device: number; // 设备数
      // }
    ],

    total: [
      { name: '受检单位数', value: 0 },
      { name: '设备数量', value: 0 },
    ],

    // 报送单位数地区分布 左上
    reportedCustomerOrgRegion: {
      axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
      seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    },

    // 设备类型数据 左中
    deviceCategory: {
      axisData: ['核医学', '放射治疗', 'X射线影像诊断', '介入放射学'], // 上、左、下、右
      seriesData: [0, 0, 0, 0],
    },

    // 受检单位地区分布 左下
    employerRegion: [
      { name: "杭州市", value: 0 },
      { name: "宁波市", value: 0 },
      { name: "温州市", value: 0 },
      { name: "嘉兴市", value: 0 },
      { name: "湖州市", value: 0 },
      { name: "绍兴市", value: 0 },
      { name: "金华市", value: 0 },
      { name: "衢州市", value: 0 },
      { name: "舟山市", value: 0 },
      { name: "台州市", value: 0 },
      { name: "丽水市", value: 0 }
    ],

    // 检测类型占比 右上
    projectType: [
      { name: '检测总数', value: 0 },
      { name: '性能检测：', value: 0 },
      { name: '场所防护：', value: 0 },
    ],

    // 不合格设备地区分布 右中
    unqualifiedDeviceRegion: [
      { name: "杭州", value: 0 },
      { name: "宁波", value: 0 },
      { name: "温州", value: 0 },
      { name: "嘉兴", value: 0 },
      { name: "湖州", value: 0 },
      { name: "绍兴", value: 0 },
      { name: "金华", value: 0 },
      { name: "衢州", value: 0 },
      { name: "舟山", value: 0 },
      { name: "台州", value: 0 },
      { name: "丽水", value: 0 }
    ],

    // 单位类别数据 右下
    employerType: [
      { value: 0, name: '企业' },
      { value: 0, name: '其他医疗机构' },
      { value: 0, name: '专科医院' },
      { value: 0, name: '其他单位' },
      { value: 0, name: '综合性医院' },
    ],

    // 中下 机构统计
    institution: [
      { name: '技术服务机构数', value: 0 },
      { name: '检测仪器数', value: 0 },
      { name: '技术服务人员数', value: 0 },
      { name: '监测报告数', value: 0 },
      { name: '不合格设备数', value: 0 },
      { name: '设备数', value: 0 },
    ],

  }),

  getters: {},

  actions: {

    // 报送单位数地区分布 左上
    async getReportedCustomerOrgRegionData(params?: { year?: string | null, code?: string }) {
      const res = await getReportedCustomerOrgRegion(params)
      this.reportedCustomerOrgRegion.axisData = res.data.map((item: any) => item.regionName.slice(0, 2))
      this.reportedCustomerOrgRegion.seriesData = res.data.map((item: any) => item.cnt)
    },

    // 设备类型数据 左中
    async getRadiationDeviceCategoryData(params?: { year?: string | null, code?: string }) {
      const res = await getRadiationDeviceCategory(params)
      const total = res.data.reduce((total: number, item: any) => total + item.categoryCount, 0)
      this.deviceCategory.axisData = res.data.map((item: any) => item.categoryName)
      this.deviceCategory.seriesData = res.data.map((item: any) => total > 0 ? Math.round(item.categoryCount / total * 100) : 0)
    },

    // 受检单位地区分布 左下
    async getRadiationEmployerRegionData(params?: { year?: string | null, code?: string }) {
      const res = await getRadiationEmployerRegion(params)
      this.employerRegion = res.data.map((item: any) => ({ name: item.regionName, value: item.cnt }))
      this.total[0].value = res.data.reduce((total: number, item: any) => total + item.cnt, 0) // 受检单位数总数
      this.mapData = res.data.map((item: any) => ({
        name: item.regionName,
        code: item.regionCode,
        value: 0,
        employer: item.cnt,
        institution: 0,
        device: 0
      }))
      const institutionList = await getInstitutionRegion(params) // 机构数地区分布
      const institutionTotal = institutionList.data.reduce((total: number, item: any) => total + item.cnt, 0)
      this.institution[0].value = institutionTotal // 技术服务机构数总数
      this.mapData.forEach((item, index) => {
        const data = institutionList.data.find((i: any) => i.regionCode === item.code)
        if (data) { item.institution = data.cnt }
      })
      const deviceList = await getDeviceRegion(params) // 设备数地区分布
      const deviceTotal = deviceList.data.reduce((total: number, item: any) => total + item.cnt, 0)
      this.institution[5].value = deviceTotal // 设备数总数
      this.total[1].value = deviceTotal // 设备数总数
      this.mapData.forEach((item, index) => {
        const data = deviceList.data.find((i: any) => i.regionCode === item.code)
        if (data) { item.device = data.cnt }
      })
    },

    // 检测类型占比 右上
    async getRadiationProjectTypeData(params?: { year?: string | null, code?: string }) {
      const res = await getRadiationProjectType(params)
      this.projectType[0].value = res.data.projectCount || 0
      this.projectType[1].value = res.data.performanceProjectCount || 0
      this.projectType[2].value = res.data.protectionProjectCount || 0
    },

    // 不合格设备地区分布 右中
    async getUnqualifiedDeviceRegionData(params?: { year?: string | null, code?: string }) {
      const res = await getUnqualifiedDeviceRegion(params)
      this.unqualifiedDeviceRegion = res.data.map((item: any) => ({ name: item.regionName.slice(0, 2), value: item.cnt }))
      this.institution[4].value = res.data.reduce((total: number, item: any) => total + item.cnt, 0) // 不合格
    },

    // 单位类别数据 右下 
    async getRadiationEmployerTypeData(params?: { year?: string | null, code?: string }) {
      const res = await getRadiationEmployerType(params)
      const total = res.data.reduce((total: number, item: any) => total + item.cnt, 0)
      this.employerType = res.data.map((item: any) => ({
        name: item.orgType,
        value: total > 0 ? + (item.cnt / total * 100).toFixed(2) : 0 // 必须为number类型
      }))
    },

    async getRadiationInstitutionsData(params?: { year?: string | null, code?: string }) {
      const res = await getRadiationInstitutions(params)
      this.institution[0].value = res.data.institutionCount || 0
      this.institution[1].value = res.data.instrumentCount || 0
      this.institution[2].value = res.data.employeeCount || 0
      const reports = await getRadiationReports(params)
      this.institution[3].value = reports.data || 0
    }

  }

})