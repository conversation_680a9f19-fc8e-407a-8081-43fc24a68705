declare type mapAreaData = {
  name: string;
  value: number;
  declaredEmployerCount: number;
  checkedEmployerCount: number;
  examCardCount: number;
}

declare type mapAreaDetail = {
  name: string;
  value: number;

  declaredEmployerCount: number;
  checkedEmployerCount: number;
  examCardCount: number;

  list: mapAreaDetailList;
}

declare type mapAreaDetailList = {
  name: string,
  value: number,
  total: number,
  percentage?: number
}[]

declare interface mapAreaCount {
  [key: string]: any
  declaredEmployerCount: number;
  checkedEmployerCount: number;
  examCardCount: number;
}

// 申报
declare interface declareCountGroupArea {
  name: string;        // 地区名称
  empCount: number;    // 申报企业数
  declareCount: number; // 申报数
  code: string          // 地区编码
}

declare interface resObj {
  name:string;
  value:number;
  [key: string]: any
}