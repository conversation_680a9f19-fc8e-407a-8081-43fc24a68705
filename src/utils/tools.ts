
export function hexToRgb(hex: string): number[] {
  let r = parseInt(hex.slice(1, 3), 16);
  let g = parseInt(hex.slice(3, 5), 16);
  let b = parseInt(hex.slice(5, 7), 16);
  return [r, g, b];
}

export function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1).toUpperCase();
}

export function interpolateColor(startColor: string, endColor: string, factor: number): string {
  let startRGB = hexToRgb(startColor);
  let endRGB = hexToRgb(endColor);
  let r = Math.round(startRGB[0] + factor * (endRGB[0] - startRGB[0]));
  let g = Math.round(startRGB[1] + factor * (endRGB[1] - startRGB[1]));
  let b = Math.round(startRGB[2] + factor * (endRGB[2] - startRGB[2]));
  return rgbToHex(r, g, b);
}

// 返回一个颜色数组，从startColor到endColor的线性渐变，数组长度为arrayLength，HEX格式
export function generateGradientColors(startColor: string, endColor: string, arrayLength: number): string[] {
  let colors = [];
  for (let i = 0; i < arrayLength; i++) {
    let factor = i / (arrayLength - 1);
    colors.push(interpolateColor(startColor, endColor, factor));
  }
  return colors;
}

// 返回根据单位收缩后的数值和单位名称
export const getUnitShrink = (arr: number[], multiple: number = 10): { unitName: string; unit: number } => {
  const max = Math.max(...arr)
  const hundred = 100 // 百
  const thousand = 1000 // 千
  const tenThousand = 10000 // 万
  const hundredThousand = 100000 // 十万
  const million = 1000000 // 百万
  const tenMillion = 10000000 // 千万

  let unitName = ''
  let unit = 1

  if (max > tenMillion * multiple) {
    unitName = '千万'
    unit = tenMillion
  } else if (max > million * multiple) {
    unitName = '百万'
    unit = million
  } else if (max > hundredThousand * multiple) {
    unitName = '十万'
    unit = hundredThousand
  } else if (max > tenThousand * multiple) {
    unitName = '万'
    unit = tenThousand
  } else if (max > thousand * multiple) {
    unitName = '千'
    unit = thousand
  } else if (max > hundred * multiple) {
    unitName = '百'
    unit = hundred
  } else {
    unitName = ''
    unit = 1
  }
  return { unitName, unit }
}