import axios from 'axios';
import type { AxiosInstance } from 'axios';
import { ElMessage } from 'element-plus';
import qs from 'qs';

import { useDashBoardStore } from '@/stores/index'

// 配置新建一个 axios 实例
const service: AxiosInstance = axios.create({
	baseURL: import.meta.env.VITE_API_URL,
	timeout: 30000,
	headers: { 'Content-Type': 'application/json' },
	paramsSerializer: {
		serialize(params) {
			return qs.stringify(params, { allowDots: true });
		},
	},
});

// 添加请求拦截器
service.interceptors.request.use(
	(config) => {
		// 将store的token添加到请求头Authorization中
		const dashBoardStore = useDashBoardStore();
		config.headers['token'] = dashBoardStore.getToken || '';
		return config;
	},
	(error) => {
		// 对请求错误做些什么
		return Promise.reject(error);
	}
);

// 添加响应拦截器
service.interceptors.response.use(
	(response) => {
		// 对响应数据做点什么
		const res = response.data;
		const isSuccess = res.success;
		if (!isSuccess) {
			ElMessage.error(res.msg)
			return Promise.reject(response);
		} else {
			return res;
		}
	},
	(error) => {
		console.log('Request error', error);
		const dashBoardStore = useDashBoardStore();
		// 如果状态码为401
		if (error.response.status == 401) {
			ElMessage.error('登录失效，请重新登录');
			// 清除token
			dashBoardStore.resetToken();
		}
		// 其他错误响应
		if (error.message.indexOf('timeout') != -1) {
			ElMessage.error('网络超时');
		} else if (error.message == 'Network Error') {
			ElMessage.error('网络连接错误');
		} else if (error.message == "Request failed with status code 500") {
			ElMessage.error('服务器错误');
		} else if (error.response.data) {
			ElMessage.error((error.response.data.message?.toString() || error.response.statusText));
		}
		else {
			ElMessage.error(error.message)
		}
		// window.location.href = '/uncertified'
		dashBoardStore.logout('/uncertified');
		return Promise.reject(error);
	}
);

// 导出 axios 实例
export default service;
