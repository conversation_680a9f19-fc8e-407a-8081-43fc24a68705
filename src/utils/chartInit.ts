// 所有图表的原型配置 用于初始化图表
export const option = {
  tooltip: {},
  grid: {
    top: '20%',
    left: '3%',
    right: '4%',
    bottom: '-0',
    containLabel: true
  },
  xAxis: {
    data: [],
    axisLabel: {
      color: '#D9D9D9',
      fontSize: 10,
      interval: 0,
    },
  },
  yAxis: {
    name: '',
    nameTextStyle: {
      color: '#D9D9D9',
    },
    type: 'value',
    max: (value: { min: number, max: number; }) => {  // 百位起最大值向上取整
      if (value.max <= 10) {
        return 10;
      }
      else return undefined;
    },
    splitLine: {
      lineStyle: {
        type: 'dashed',
        color: '#333',
      },
    },
    axisLabel: {
      color: '#D9D9D9',
      fontSize: 10,
    },
  },
  series: [
    {
      name: '',
      type: 'pictorialBar',
      symbol: "path://M0 3.8120381832122803a3.8120381832122803 3.8120381832122803 0 0 1 3.8120381832122803 -3.8120381832122803h7.873899936676025a3.8120381832122803 3.8120381832122803 0 0 1 3.8120381832122803 3.8120381832122803v0a3.8120381832122803 3.8120381832122803 0 0 1 -3.8120381832122803 3.8120381832122803h-7.873899936676025a3.8120381832122803 3.8120381832122803 0 0 1 -3.8120381832122803 -3.8120381832122803z",
      symbolSize: [15.5, 7.6],
      symbolRepeat: true,
      barMinHeight: 7.6,
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 8
      },
      itemStyle: {
        color: '#18CACA'
      },
      data: []
    }
  ]
};
