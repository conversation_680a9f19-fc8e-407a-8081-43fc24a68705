<template>
  <div class="OrgStatistic-outerContainer">
    <div class="statisticItem " v-for="(item, i) in titleList" :key="i">
      <div class="ItemTitle">
        <div class="text">{{ item }}</div>
      </div>
      <div class="gap"></div>
      <div class="ItemContent">
        <Pie3D :series-data="seriesDataList[i]"></Pie3D>
      </div>
    </div>

  </div>
</template>


<script setup lang="ts" name="OrgStatistic">
import Pie3D from '@/components/charts/Pie3D.vue';

const titleList = ['职业病企业类型', '职业病企业规模']
const seriesDataList = [
  [
    { value: 421 / 453 * 100, name: '内资企业' },
    { value: 9 / 453 * 100, name: '港、澳、台商投资企业' },
    { value: 23 / 453 * 100, name: '外商投资企业' }
  ],
  [
    { value: 31 / 453 * 100, name: '大型' },
    { value: 52 / 453 * 100, name: '中型' },
    { value: 351 / 453 * 100, name: '小型' },
    { value: 19 / 453 * 100, name: '微型' }
  ]
]

</script>

<style scoped lang="scss">
.OrgStatistic-outerContainer {
  width: 100%;
  height: 100%;
  display: flex;
  padding: vh(8);

  .statisticItem {
    width: calc(100% / 2 - 1px); // 不能直接使用 flex: 1; 会导致子元素大于父元素
    height: 100%;
    display: flex;
    flex-direction: column;

    .gap {
      width: 100%;
      height: vh(12);
    }

    .ItemTitle {
      height: vh(21);
      width: 100%;
      padding-left: 4%;

      background-image: url(@/assets/images/ProgressBarItem2.png);
      background-size: 85% 100%;
      background-repeat: no-repeat;

      display: flex;
      align-items: center;

      .text {
        font-size: vh(16);
        letter-spacing: 0.08px;
        font-family: YouSheBiaoTiHei;
        font-weight: normal;
        line-height: normal;
        color: linear-gradient(180deg, #FFFFFF 32%, #18CACA 87%);
        background: linear-gradient(180deg, #FFFFFF 32%, #18CACA 87%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }


    }

    .ItemContent {
      width: 100%;
      height: calc(100% - vh(21 + 7));
    }
  }
}
</style>