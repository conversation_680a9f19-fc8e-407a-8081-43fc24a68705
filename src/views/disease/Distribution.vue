<template>
    <div class="OrgStatistic-outerContainer">
        <div class="statisticItem " v-for="(item, i) in titleList" :key="i">
            <div class="gap"></div>
            <div class="ItemTitle">
                <img class="icon" :src="iconList[i]"></img>
                <div class="text">{{ item }}</div>
            </div>
            <div class="ItemContent">
                <Bar3D :title="titleList[i]" :axisData="axisData" :seriesData="seriesDataList[i]" showSymbol xAxisRotate
                    :barWidth="12" />
            </div>
        </div>

    </div>
</template>


<script setup lang="ts" name="OrgStatistic">
// import Line from '@/components/charts/Line.vue'
import Bar3D from '@/components/charts/Bar3D.vue'

import icon1 from '@/assets/images/尘肺病.svg'
import icon2 from '@/assets/images/耳鼻喉.svg'
import icon3 from '@/assets/images/职业中毒.svg'
import icon4 from '@/assets/images/物理因素.svg'

const axisData = ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],
    seriesData = [0, 0, 0, 0, 0, 0, 0]
const seriesDataList = [
    [309, 495, 364, 236, 323, 650, 234],
    [65, 66, 61, 98, 100, 129, 129],
    [84, 19, 25, 42, 17, 24, 14],
    [15, 9, 22, 25, 92, 41, 64]
]

const titleList = ['职业性尘肺病', '职业性耳鼻喉口腔疾病', '职业性化学中毒', '物理因素所致职业病']
const iconList = [icon1, icon2, icon3, icon4]

</script>

<style scoped lang="scss">
.OrgStatistic-outerContainer {
    width: 100%;
    height: 100%;
    display: flex;
    gap: vw(16);

    .statisticItem {
        width: calc(100% / 4 - 16px * 3 / 4 - 1px); // 不能直接使用 flex: 1; 会导致子元素大于父元素
        height: 100%;
        display: flex;
        flex-direction: column;

        .gap {
            width: 100%;
            height: vh(8);
        }

        .ItemTitle {
            height: vh(21);
            width: 100%;
            padding-left: 4%;

            background-image: url(@/assets/images/ProgressBarItem2.png);
            background-size: 85% 100%;
            background-repeat: no-repeat;

            display: flex;
            align-items: center;

            .icon {
                height: 100%;
                // height: vh(21 * 2);
            }

            .text {
                font-size: vh(16);
                letter-spacing: 0.08px;
                font-family: YouSheBiaoTiHei;
                font-weight: normal;
                line-height: normal;
                color: linear-gradient(180deg, #FFFFFF 32%, #18CACA 87%);
                background: linear-gradient(180deg, #FFFFFF 32%, #18CACA 87%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                text-fill-color: transparent;
            }


        }

        .ItemContent {
            width: 100%;
            height: calc(100% - vh(21 + 7));
        }
    }
}
</style>