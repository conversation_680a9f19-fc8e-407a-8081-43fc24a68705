<template>
  <div class="statisticItem center">
    <div class="gaugeItem-wrap">
      <div class="gaugeItem">
        <GaugeCircle :data="51.66"></GaugeCircle>
        <div>
          <p style="text-align: center;">职业性</p>
          <p style="text-align: center;">尘肺病</p>
        </div>
      </div>
      <div class="gaugeItem">
        <GaugeCircle :data="28.48"></GaugeCircle>
        <div>
          <p style="text-align: center;">职业性耳鼻喉</p>
          <p style="text-align: center;">口腔疾病</p>
        </div>
      </div>
    </div>
    <div class=" gaugeItem-wrap">
      <div class="gaugeItem">
        <GaugeCircle :data="3.09"></GaugeCircle>
        <div>
          <p style="text-align: center;">职业性</p>
          <p style="text-align: center;">化学中毒</p>
        </div>
      </div>
      <div class="gaugeItem">
        <GaugeCircle :data="14.13"></GaugeCircle>
        <div>
          <p style="text-align: center;">职业性</p>
          <p style="text-align: center;">所致职业病</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="Gauges">
import GaugeCircle from '@/components/charts/GaugeCircle.vue';

</script>

<style lang="scss" scoped>
.statisticItem {
  width: 100%;
  height: 100%;

  &.center {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .gaugeItem-wrap {
    width: 100%;
    height: 50%;
    display: flex;
    justify-content: center;
    align-items: center;

    font-size: vh(12);
    color: #fff;

    .gaugeItem {
      width: 50%;
      height: 100%;
      padding: vh(10);
      box-sizing: border-box;
      display: flex;
      // flex-direction: column;
      justify-content: space-evenly;
      align-items: center;

      &>div {
        flex: 1;
        text-align: center;

        font-size: 14px;
        font-weight: 350;
        line-height: normal;
        letter-spacing: 0.15px;
        font-feature-settings: "kern" on;
        color: #FFFFFF;
      }
    }
  }

}
</style>