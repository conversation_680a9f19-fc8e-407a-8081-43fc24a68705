<template>
  <div class="dashboard-container">
    <Aside class="index-left" :topTitle="'职业病人数地区分布'" :centerTitle="'职业病发病人数历年趋势'" :bottomTitle="'职业病病种分布'">
      <template #top>
        <Bar3D :gap="false" :title="'职业病人数'" showNumber
          :axis-data="['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水']"
          :seriesData="[61, 58, 22, 43, 49, 23, 19, 36, 3, 123, 16]" />
      </template>
      <template #center>
        <DoubleLine :axisData="['2017年', '2018年', '2019年', '2020年', '2021年', '2022年', '2023年', '2024年']"
          :series-data1="[395, 309, 495, 364, 236, 323, 650, 234]"
          :series-data2="[577, 485, 596, 485, 411, 541, 863, 453]" />
      </template>
      <template #bottom>
        <Gauges></Gauges>
      </template>
    </Aside>
    <Center class="index-center" :title="'职业病新发病例历年趋势'">
      <template #top>
        <TotalNum v-for="(item, i) in total" :key="i" :title="item.name" :total="item.value"></TotalNum>
      </template>
      <template #map>
        <CenterMap></CenterMap>
      </template>
      <template #bottom>
        <Distribution></Distribution>
      </template>
    </Center>
    <Aside class="index-right" :topTitle="'疑似职业病进入诊断程序比例'" :centerTitle="'职业病行业分布'" :bottomTitle="'职业病企业分布'">
      <template #top>
        <Bar3D :gap="false" :title="'职业病人数'" showNumber yAxisName="%"
          :axis-data="['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水']"
          :seriesData="[37, 43, 15, 64, 50, 40, 13, 53, 20, 48, 35]" />
        <!-- <ProgressBars></ProgressBars> -->
      </template>
      <template #center>
        <Radar
          :axisData='["A农、林、牧、渔业", "B采矿业", "C制造业", "E建筑业", "F批发和零售业", "G交通运输、仓储和邮政业", "H住宿和餐饮业", "I信息传输、软件和信息技术服务业", "L租赁和商务服务业", "N水利、环境和公共设施管理业", "O居民服务、修理和其他服务业", "S公共管理、社会保障和社会组织"]'
          :seriesData="[1, 30, 233, 36, 4, 4, 1, 1, 3, 2, 1, 137].map(item => (item / 453 * 100).toFixed(2))"
          :font-size="8" :radar="{ radius: 60, axisNameGap: 4 }"></Radar>
      </template>
      <template #bottom>
        <Pies></Pies>
      </template>
    </Aside>
  </div>
</template>

<script setup lang="ts" name="dashBoard">
import { ref, watch } from "vue";
/* 布局、具体组件、数据分开 ，index中直接对整体进行布局，插入需要的组件，组件中只负责展示数据 */
// 布局组件
import Center from "@/components/layout/Center.vue";
import Aside from "@/components/layout/Aside.vue";
// 组件
import CenterMap from "./CenterMap.vue";
import TotalNum from "@/components/TotalNum.vue";
// 图表组件
import Distribution from "./Distribution.vue";
import Radar from "@/components/charts/Radar.vue";
import Gauges from "./Gauges.vue"
import ProgressBars from "./ProgressBars.vue";
import Pies from "./Pies.vue";
import Bar3D from "@/components/charts/Bar3D.vue";
import DoubleLine from "@/components/charts/DoubleLine.vue";
// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
// const dashBoardStore = useDashBoardStore();
const homeStore = useDashBoardStore();

// 地图上方地区总数
const total = ref([
  { name: '职业病申请诊断人数', value: 1398 },
  { name: '新发职业病人数', value: 453 },
  { name: '职业病鉴定人数', value: 109 },
])


</script>

<style scoped lang="scss">
.dashboard-container {
  width: 100%;
  height: 100%;
  display: flex;

  .index-left {
    width: 25%;
  }

  .index-center {
    width: 50%;
  }

  .index-right {
    width: 25%;

    .yearSelect-btn {

      position: absolute;
      top: calc(-1.2vw * 1.33 - 4px);
      left: calc(4.44vw * 1.33 *2);

      :deep(.el-input) {
        font-size: vw(18);
      }

      :deep(.el-date-editor) {
        --el-date-editor-width: calc(4.44vw * 1.33);
        --el-input-height: calc(1.2vw * 1.33);
        // --el-date-editor-width: vh(105 * 1.2);
        // --el-input-height: vh(28 * 1.2);
      }

      :deep(.el-input__wrapper) {
        box-shadow: none;
        background-color: transparent;
        padding: 0 vw(25);

        background-image: url(@/assets/images/yearSelect.svg);
        background-repeat: no-repeat;
        background-size: auto 100%;

        cursor: pointer;
      }

      :deep(.el-input__inner) {
        width: calc(3.2vw);
        height: 1.2vw;
        line-height: 1.2vw;
        -webkit-appearance: none;
        background-color: transparent;

        border: none;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        font-size: 1em !important;
        font-weight: Medium;
        text-align: left;
        color: #18caca;
        display: inline-block;
        font-size: inherit;
        outline: 0;
        padding: 0;
        -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        cursor: pointer;

        &::placeholder {
          color: #18caca;
        }
      }

      :deep(.el-select .el-input.is-focus .el-input__wrapper) {
        box-shadow: none;
      }

      :deep(.el-input__prefix) {
        display: none;
      }

      :deep(.el-input__suffix-inner > :first-child) {
        margin-left: 0px;
      }
    }
  }
}
</style>
