<template>
  <div class="wrap">
    <div ref="lineChartRef" class="lineChart"></div>
  </div>
</template>
<script setup lang="ts" name="DoubleBar">
import * as echarts from 'echarts';
import type { EChartsType, EChartsOption } from 'echarts';
import { ref, onMounted, onBeforeUnmount, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'

import _ from 'lodash'

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { splitLineColor } = storeToRefs(dashBoardStore)

const props = withDefaults(defineProps<{
  title?: string;
  axisData?: any;   // x轴数据
  seriesData1?: any;  // 蓝色
  seriesData1Name?: string;
  seriesData2?: any;  // 黄色
  seriesData2Name?: string;
  gap?: boolean; // 双柱是否重叠
  showNumber?: boolean; // 顶部是否显示数值
}>(), {
  title: 'DoubleBarChart',
  axisData: ['矽尘', '煤尘', '石棉粉尘', '滑石粉尘', '白炭黑粉尘', '铅及其化合物', '苯', '噪声'],
  seriesData1Name: '企业数',
  seriesData1: [0, 0, 0, 0, 0, 0, 0, 0],
  seriesData2Name: '接害人数',
  seriesData2: [0, 0, 0, 0, 0, 0, 0, 0],
  gap: true,
  showNumber: false,
});

const lineChartRef = ref()
const chartInstance = ref<EChartsType>();

const option = {
  grid: {
    top: '17.5%',
    left: '2.5%',
    bottom: '5%',
    right: '5%',
    containLabel: true,
  },
  legend: [
    {
      data: [
        { name: props.seriesData1Name, itemStyle: { color: '#17E3E3' } },
        { name: props.seriesData2Name, itemStyle: { color: '#FAC858' } },
      ],
      textStyle: {
        color: '#ccc'
      },
      icon: 'roundRect',
      itemHeight: 8,
      itemWidth: 10,
      right: '5%',
    },
  ],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    className: 'custom-tooltip-box',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 12
    },
    formatter: `
    <div style="
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
      justify-content: space-around;"
    >
      <p>{b}</p>
      <p>{a1}：<span style="color:#17E3E3;font-weight:bold;">{c1}</span></p>
      <p>{a4}：<span style="color:#FAC858;font-weight:bold;">{c4}</span></p>
    </div>
    `
  },
  xAxis: {
    type: 'category',
    data: props.axisData,
    axisTick: {
      alignWithLabel: true,
      show: false,
    },
    axisLine: {
      show: false,
    },
    axisLabel: {
      textStyle: {
        color: '#ddd',
        fontSize: 8,
      },
      interval: 0,
      margin: 12,
    },
  },
  yAxis: [{
    name: '',
    type: 'value',
    splitLine: { show: false, },
    axisLabel: { show: false, },
    position: "left",
  },
  {
    name: '',
    type: 'value',
    splitLine: { show: false, },
    axisLabel: { show: false, },
    position: 'right',
  }
  ],
  series: [
    {
      name: props.seriesData1Name,
      type: 'pictorialBar',
      silent: true,
      symbolSize: [50, 12],
      symbolOffset: ['-60%', 5],
      z: 1,
      color: '#25C4F8',
      data: [],
      yAxisIndex: 0,
    },
    {
      name: props.seriesData1Name,
      type: 'bar',
      barWidth: '50',
      barGap: '20%',
      silent: true,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(37, 196, 248, 0)',
          },
          {
            offset: 1,
            color: '#25C4F8',
          },
        ]),
        opacity: 1,
      },
      data: [],
      yAxisIndex: 0,
      z: 2,
    },
    {
      name: props.seriesData1Name,
      type: 'pictorialBar',
      silent: true,
      symbolSize: [50, 12],
      symbolOffset: ['-60%', -4],
      symbolPosition: 'end',
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'DS-Digital'
      },
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(37, 196, 248, 0)',
        },
        {
          offset: 1,
          color: '#17E3E3',
        },
      ]),
      data: [],
      yAxisIndex: 0,
      z: 3,
    },
    // 右柱
    {
      name: props.seriesData2Name,
      type: 'pictorialBar',
      silent: true,
      symbolSize: [50, 12],
      symbolOffset: ['60%', 5],
      z: 1,
      color: '#E3DF82',
      data: [],
      yAxisIndex: 1,
    },
    {
      name: props.seriesData2Name,
      type: 'bar',
      barWidth: '50',
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(248, 211, 3, 0)',
          },
          {
            offset: 1,
            color: '#FFD33C',
          },
        ]),
        opacity: 1,
      },
      data: [],
      yAxisIndex: 1,
      z: 2,
    },
    {
      name: props.seriesData2Name,
      type: 'pictorialBar',
      silent: true,
      symbolSize: [50, 12],
      symbolOffset: ['60%', -4],
      symbolPosition: 'end',
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 12,
        fontFamily: 'DS-Digital'
      },
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(248, 211, 3, 0)',
        },
        {
          offset: 1,
          color: '#FFD33C',
        },
      ]),
      z: 3,
      yAxisIndex: 1,
      data: [],
    },
  ],
};

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option as EChartsOption);
  }
};

//初始化
const init = () => {
  if (!lineChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(lineChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(lineChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option.xAxis.data = props.axisData;  // x轴数据
    option.series[0].data = new Array(props.axisData.length).fill(0);
    option.series[1].data = props.seriesData1;
    option.series[2].data = props.seriesData1;
    option.series[3].data = new Array(props.axisData.length).fill(0);
    option.series[4].data = props.seriesData2;
    option.series[5].data = props.seriesData2;
    // 双柱是否重叠
    option.series[0].symbolOffset = props.gap ? ['-60%', 5] : ['0%', 5];
    option.series[1].barGap = props.gap ? '20%' : '-100%';
    option.series[2].symbolOffset = props.gap ? ['-60%', -4] : ['0%', -4];
    option.series[3].symbolOffset = props.gap ? ['60%', 5] : ['0%', 5];
    option.series[4].barGap = props.gap ? '20%' : '-100%';
    option.series[5].symbolOffset = props.gap ? ['60%', -4] : ['0%', -4];
    // 顶部是否显示数值
    // @ts-ignore
    // option.series[1].label.show = props.showNumber;
    // @ts-ignore
    // option.series[4].label.show = props.showNumber;

    draw()
  },
  { immediate: true, deep: true }
)

const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.lineChart {
  width: 100%;
  height: 100%;
}

.wrap {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;
}
</style>

<style lang="scss">
.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>