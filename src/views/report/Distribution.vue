<template>
    <div class="OrgStatistic-outerContainer">
        <!-- <div class="statisticItem ">
            <Gauge :title="'检测率'" :data="declareSituation.detectionRate.toFixed(2)"></Gauge>
        </div>
        <div class="statisticItem center">
            <div class=" gaugeItem-wrap">
                <div class="gaugeItem">
                    <GaugeCircle :data="(declareSituation.staffTrainRate * 100).toFixed(2)"></GaugeCircle>
                    劳动者培训率
                </div>
            </div>
            <div class=" gaugeItem-wrap">
                <div class="gaugeItem">
                    <GaugeCircle :data="(declareSituation.leaderTrainRate * 100).toFixed(2)"></GaugeCircle>
                    法人代表培训率
                </div>
                <div class="gaugeItem">
                    <GaugeCircle :data="(declareSituation.managerTrainRate * 100).toFixed(2)"></GaugeCircle>
                    负责人培训率
                </div>
            </div>
        </div>
        <div class="statisticItem ">
            <Gauge :title="'体检率'" :data="declareSituation.examinationRate.toFixed(2)"></Gauge>
        </div> -->
        <ReportDoubleBar :gap="false" showNumber :axisData="empCountHazard.axisData"
            :seriesData1="empCountHazard.seriesData1" :seriesData2="empCountHazard.seriesData2" />
    </div>
</template>


<script setup lang="ts" name="OrgStatistic">
// import Gauge from '@/components/charts/Gauge.vue';
// import GaugeCircle from '@/components/charts/GaugeCircle.vue';
import ReportDoubleBar from './ReportDoubleBar.vue';

import { storeToRefs } from 'pinia';
import { useReportStore } from '@/stores/report'
const reportStore = useReportStore()
const { empCountHazard } = storeToRefs(reportStore)

</script>

<style scoped lang="scss">
.OrgStatistic-outerContainer {
    width: 100%;
    height: 100%;
    display: flex;

    .statisticItem {
        width: calc(100% /3);
        height: 100%;

        &.center {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .gaugeItem-wrap {
            width: 100%;
            height: 50%;
            display: flex;
            justify-content: center;
            align-items: center;

            font-size: vh(12);
            color: #fff;

            .gaugeItem {
                width: 50%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
        }

    }

}
</style>