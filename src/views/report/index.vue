<template>
  <div class="dashboard-container">
    <Aside class="index-left" :topTitle="'申报企业地区分布'" :centerTitle="'初次申报地区分布趋势'" :bottomTitle="'申报企业行业分布'">
      <template #top>
        <Bar3D :title="'申报企业数'" :axisData="empArea.axisData" :seriesData="empArea.seriesData"
          :showYAxisSplitLine="false" />
      </template>
      <template #center>
        <!-- <Rings></Rings> -->
        <Line :title="'申报数'" :axisData="firstDeclareTrend.axisData" :seriesData="firstDeclareTrend.seriesData"
          showSymbol :showYAxisSplitLine="false" :colorIndex="1"></Line>
      </template>
      <template #bottom>
        <ProgressList :seriesData="empIndustry" titleShrink></ProgressList>
      </template>
    </Aside>
    <Center class="index-center" :title="'申报企业情况分布'">
      <template #top>
        <TotalNum v-for="(item, i) in total" :key="i" :title="item.name" :total="item.value"></TotalNum>
      </template>
      <template #map>
        <CenterMap></CenterMap>
      </template>
      <template #bottom>
        <Distribution></Distribution>
      </template>
    </Center>
    <Aside class="index-right" :topTitle="'每月申报趋势'" :centerTitle="'申报企业经济类型分布'" :bottomTitle="'申报企业规模分布'">
      <template #top>
        <Line :title="'申报数'" :axisData="declareTrend.axisData" :seriesData="declareTrend.seriesData" showSymbol
          :showYAxisSplitLine="false"></Line>
      </template>
      <template #center>
        <Pyramid :axisData="empEconomic.axisData" :seriesData="empEconomic.seriesData" isPercent allRight />
      </template>
      <template #bottom>
        <Radar :axisData="empEnterpriseSize.axisData" :seriesData="empEnterpriseSize.seriesData"></Radar>
      </template>
    </Aside>
  </div>
</template>

<script setup lang="ts" name="dashBoard">
import { ref, watch } from "vue";
/* 布局、具体组件、数据分开 ，index中直接对整体进行布局，插入需要的组件，组件中只负责展示数据 */
// 布局组件
import Center from "@/components/layout/Center.vue";
import Aside from "@/components/layout/Aside.vue";
// 组件
import CenterMap from "./CenterMap.vue";
import TotalNum from "@/components/TotalNum.vue";
// 图表组件
import Distribution from "./Distribution.vue";
import Pyramid from "@/components/charts/Pyramid.vue";
import Radar from "@/components/charts/Radar.vue";
import ProgressList from "@/components/charts/ProgressList.vue";
import Line from "@/components/charts/Line.vue";
import Bar3D from "@/components/charts/Bar3D.vue";
// pinia
import { storeToRefs } from 'pinia';
import { useReportStore } from '@/stores/report'
// const dashBoardStore = useDashBoardStore();
const reportStore = useReportStore()

const {
  empTotal,
  empArea,
  empIndustry,
  declareTrend,
  empEconomic,
  empEnterpriseSize,
  firstDeclareTrend,
} = storeToRefs(reportStore)

// 地图上方地区总数
const total = ref([
  { name: '申报企业数', value: empTotal },
])

</script>

<style scoped lang="scss">
.dashboard-container {
  width: 100%;
  height: 100%;
  display: flex;

  .index-left {
    width: 25%;
  }

  .index-center {
    width: 50%;
  }

  .index-right {
    width: 25%;

    .yearSelect-btn {

      position: absolute;
      top: calc(-1.2vw * 1.33 - 4px);
      left: calc(4.44vw * 1.33 *2);

      :deep(.el-input) {
        font-size: vw(18);
      }

      :deep(.el-date-editor) {
        --el-date-editor-width: calc(4.44vw * 1.33);
        --el-input-height: calc(1.2vw * 1.33);
        // --el-date-editor-width: vh(105 * 1.2);
        // --el-input-height: vh(28 * 1.2);
      }

      :deep(.el-input__wrapper) {
        box-shadow: none;
        background-color: transparent;
        padding: 0 vw(25);

        background-image: url(@/assets/images/yearSelect.svg);
        background-repeat: no-repeat;
        background-size: auto 100%;

        cursor: pointer;
      }

      :deep(.el-input__inner) {
        width: calc(3.2vw);
        height: 1.2vw;
        line-height: 1.2vw;
        -webkit-appearance: none;
        background-color: transparent;

        border: none;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        font-size: 1em !important;
        font-weight: Medium;
        text-align: left;
        color: #18caca;
        display: inline-block;
        font-size: inherit;
        outline: 0;
        padding: 0;
        -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        cursor: pointer;

        &::placeholder {
          color: #18caca;
        }
      }

      :deep(.el-select .el-input.is-focus .el-input__wrapper) {
        box-shadow: none;
      }

      :deep(.el-input__prefix) {
        display: none;
      }

      :deep(.el-input__suffix-inner > :first-child) {
        margin-left: 0px;
      }
    }
  }
}
</style>
