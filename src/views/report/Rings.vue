<template>
  <div class="Rings-outerContainer">
    <div class="ringItem" v-for="(item, i) in titleList" :key="i">
      <!-- <div class="ringItem"> -->
      <RingPie :title="item.name" :icon="item.icon" :total="item.total" :value="item.value"></RingPie>
    </div>
  </div>
</template>


<script setup lang="ts" name="Rings">
import RingPie from '@/components/charts/RingPie.vue'

import icon1 from '@/assets/images/矿山2.svg'
import icon2 from '@/assets/images/煤矿2.svg'
import icon3 from '@/assets/images/非煤矿2.svg'
import icon4 from '@/assets/images/冶金2.svg'
import icon5 from '@/assets/images/水泥2.svg'
import icon6 from '@/assets/images/陶瓷2.svg'
import icon7 from '@/assets/images/石材加工2.svg'
import icon8 from '@/assets/images/建材.svg'

const titleList = [
  { name: '矿山', total: 998, value: 123, icon: icon1 },
  { name: '煤矿', total: 998, value: 123, icon: icon2 },
  { name: '非煤矿', total: 998, value: 123, icon: icon3 },
  { name: '冶金', total: 998, value: 123, icon: icon4 },
  { name: '水泥', total: 998, value: 123, icon: icon5 },
  { name: '陶瓷', total: 998, value: 123, icon: icon6 },
  { name: '石材加工', total: 998, value: 123, icon: icon7 },
  { name: '建材', total: 998, value: 123, icon: icon8 },
]

</script>

<style scoped lang="scss">
.Rings-outerContainer {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-evenly;

  .ringItem {
    width: calc(100% / 4 - 1px);
    height: 50%;
  }
}
</style>