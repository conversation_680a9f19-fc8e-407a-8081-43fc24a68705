<script setup lang="ts">
import Error from './Error.vue'
// import { usePermissionStore } from '@/store/modules/permission'
import { useRouter } from 'vue-router'

const { push } = useRouter()

// const permissionStore = usePermissionStore()

const errorClick = () => {
  // push(permissionStore.addRouters[0]?.path as string)
  push('/')
}
</script>

<template>
  <Error @error-click="errorClick" />
</template>
