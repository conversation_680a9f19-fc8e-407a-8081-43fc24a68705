<template>
  <div class="Error-wrap">
    <div class="Error-content">
      <img width="350" :src="errorMap[type].url" ></img>
      <div style="font-size: 14px;" class="text-[var(--el-color-info)]">{{ errorMap[type].message }}</div>
      <div style="margin-top: 20px;" v-if="showBtn">
        <ElButton type="primary" @click="btnClick">{{ errorMap[type].buttonText }}</ElButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import pageError from '@/assets/images/404.svg'
import networkError from '@/assets/images/500.svg'
import noPermission from '@/assets/images/403.svg'
import { ElButton } from 'element-plus'

interface ErrorMap {
  url: string
  message: string
  buttonText: string
}


const errorMap: { [key: string]: ErrorMap } = {
  '404': {
    url: pageError,
    message: '抱歉，您访问的页面不存在。',
    buttonText: '返回首页'
  },
  '500': {
    url: networkError,
    message: '抱歉，服务器报告错误。',
    buttonText: '返回首页'
  },
  '401': {
    url: noPermission,
    message: `登录已过期，请通过门户重新登录。`,
    buttonText: '返回统一门户'
  }
}

const props = withDefaults(defineProps<{ type?: '401' | '404' | '500', showBtn?: boolean }>(), {
  type: '404',
  showBtn: true
});

const emit = defineEmits(['errorClick'])

const btnClick = () => {
  emit('errorClick', props.type)
}
</script>

<style scoped lang="scss">
.Error-wrap {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;

  position: relative;
  top: -16px;

  .Error-content{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
}
</style>