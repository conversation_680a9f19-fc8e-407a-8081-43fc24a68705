<template>
  <div class="dashboard-container">

    <Aside class="index-left" :topTitle="'报送单位数地区分布'" :centerTitle="'设备类型数据'" :bottomTitle="'受检单位地区分布'">
      <template #top>
        <Bar3D title="报送单位数" :axisData="reportedCustomerOrgRegion.axisData"
          :seriesData="reportedCustomerOrgRegion.seriesData" />
      </template>
      <template #center>
        <Radar :axisData="deviceCategory.axisData" :seriesData="deviceCategory.seriesData" />
      </template>
      <template #bottom>
        <Pie1 title="受检单位数" :seriesData="employerRegion" />
      </template>
    </Aside>

    <Center class="index-center">
      <template #top>
        <TotalNum v-for="(item, i) in total" :key="i" :title="item.name" :total="item.value"></TotalNum>
      </template>
      <template #map>
        <CenterMap></CenterMap>
      </template>
      <template #bottom>
        <StatisticCards :seriesData="institution" />
      </template>
    </Center>

    <Aside class="index-right" :topTitle="'检测类型占比'" :centerTitle="'不合格设备数地区分布'" :bottomTitle="'单位类别数据图'">
      <template #top>
        <NumberAndPie :list="projectType" />
      </template>
      <template #center>
        <ProgressList :seriesData="unqualifiedDeviceRegion"></ProgressList>
      </template>
      <template #bottom>
        <Pie3D :seriesData="employerType" />
      </template>
    </Aside>
  </div>
</template>

<script setup lang="ts" name="dashBoard">
import { ref, onMounted, onBeforeUnmount, watch } from "vue";
// 布局组件
import Center from "@/components/layout/Center.vue";
import Aside from "@/components/layout/Aside.vue";
// 组件
import CenterMap from "./CenterMap.vue";
import TotalNum from "@/components/TotalNum.vue";
// 图表组件
import Pie1 from "@/components/charts/Pie1.vue";
import ProgressList from "@/components/charts/ProgressList.vue";
import NumberAndPie from "./NumberAndPie.vue";
import Pie3D from "@/components/charts/Pie3D.vue";
import Radar from "@/components/charts/Radar.vue";
import StatisticCards from "@/components/charts/StatisticCards.vue";
import Bar3D from "@/components/charts/Bar3D.vue";
// pinia
import { storeToRefs } from 'pinia';
import { useMedicalRadiationStore } from '@/stores/medicalRadiation';
const radiationStore = useMedicalRadiationStore();

const {
  reportedCustomerOrgRegion, deviceCategory, employerRegion,
  projectType, unqualifiedDeviceRegion, employerType,
  total, institution
} = storeToRefs(radiationStore);

</script>

<style scoped lang="scss">
.dashboard-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;

  .index-left {
    width: 25%;
  }

  .index-center {
    width: 50%;
  }

  .index-right {
    width: 25%;

    .yearSelect-btn {

      position: absolute;
      top: calc(-1.2vw * 1.33 - 4px);
      left: calc(4.44vw * 1.33 *2);

      :deep(.el-input) {
        font-size: vw(18);
      }

      :deep(.el-date-editor) {
        --el-date-editor-width: calc(4.44vw * 1.33);
        --el-input-height: calc(1.2vw * 1.33);
        // --el-date-editor-width: vh(105 * 1.2);
        // --el-input-height: vh(28 * 1.2);
      }

      :deep(.el-input__wrapper) {
        box-shadow: none;
        background-color: transparent;
        padding: 0 vw(25);

        background-image: url(@/assets/images/yearSelect.svg);
        background-repeat: no-repeat;
        background-size: auto 100%;

        cursor: pointer;
      }

      :deep(.el-input__inner) {
        width: calc(3.2vw);
        height: 1.2vw;
        line-height: 1.2vw;
        -webkit-appearance: none;
        background-color: transparent;

        border: none;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        font-size: 1em !important;
        font-weight: Medium;
        text-align: left;
        color: #18caca;
        display: inline-block;
        font-size: inherit;
        outline: 0;
        padding: 0;
        -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        cursor: pointer;

        &::placeholder {
          color: #18caca;
        }
      }

      :deep(.el-select .el-input.is-focus .el-input__wrapper) {
        box-shadow: none;
      }

      :deep(.el-input__prefix) {
        display: none;
      }

      :deep(.el-input__suffix-inner > :first-child) {
        margin-left: 0px;
      }
    }
  }
}
</style>
