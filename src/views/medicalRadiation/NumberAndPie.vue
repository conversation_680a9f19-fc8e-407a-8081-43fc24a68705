<template>
  <div class="wrap">
    <div class="numbers">
      <div class="numbers-item" v-for="(item, i) in props.list" :key="i">
        <div class="name">{{ item.name }}</div>
        <div class="number">
          <div class="letter" v-for="(ele, j) in letters[i]" :key="j">{{ ele }}</div>
        </div>
      </div>
    </div>
    <div class="pie">
      <div ref="pieChartRef" class="pieChart"></div>
    </div>
  </div>
</template>

<script setup lang="ts" name="NumberAndPie">
import * as echarts from 'echarts';
import type { EChartsType } from 'echarts';
import { ref, onMounted, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'
// pinia
import { storeToRefs } from 'pinia'
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { offsetWidth } = storeToRefs(dashBoardStore)

const props = withDefaults(defineProps<{ title?: string, list?: any; }>(), {
  title: '性能检测占比',
  list: [
    { name: '检测总数', value: 0 },
    { name: '性能检测：', value: 0 },
    { name: '场所防护：', value: 0 },
  ]
});
const letters = ref<string[][]>([])

const pieChartRef = ref()
const chartInstance = ref<EChartsType>();

// const color = ['rgba(255, 211, 60, 1)','rgba(14, 219, 213, 1)','rgba(26, 71, 88, 1)']

const option = {
  // color,

  series: [
    {
      type: 'pie',
      radius: ['88%', '90%'],
      emphasis: { scale: false },
      clockwise: false,
      itemStyle: {
        shadowBlur: 20,
        shadowColor: 'rgba(26, 71, 88, 1)',
        color: 'rgba(26, 71, 88, 1)'
      },
      label: {
        show: false
      },
      data: [100]
    },

    {
      type: 'pie',
      zlevel: 3,
      silent: true,
      radius: ['70%', '80%'],
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      data: _pie()
    },
    {
      name: 'Access From',
      type: 'pie',
      radius: ['48%', '60%'],
      startAngle: 270,
      endAngle: 0,
      itemStyle: {
        borderRadius: 999,
        color: 'rgba(255, 211, 60, 1)'
      },
      silent: true,
      label: {
        show: false
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 1048, name: 'Search Engine' },
      ]
    }
  ],
  graphic: {
    elements: [
      {
        type: "text",
        left: "center",
        top: "55%",
        style: {
          text: props.title,
          textAlign: "center",
          fill: "rgba(13, 255, 250, 1)",
          fontSize: 10,
        },
      },
      {
        type: "text",
        left: "center",
        top: "37.5%",
        style: {
          text: '',
          textAlign: "center",
          fill: "rgba(13, 255, 250, 1)",
          fontSize: 30,
        },
      },
    ]
  }
};

function _pie() {
  let dataArr = [];
  for (var i = 0; i < 40; i++) {
    if (i % 2 === 0) {
      dataArr.push({
        name: (i + 1).toString(),
        value: 25,
        itemStyle: {
          color: "rgba(14, 219, 213, 1)",
          borderWidth: 0,
          borderColor: "rgba(0,0,0,0)"
        }
      })
    } else {
      dataArr.push({
        name: (i + 1).toString(),
        value: 15,
        itemStyle: {
          color: "rgba(0,0,0,0)",
          borderWidth: 0,
          borderColor: "rgba(0,0,0,0)"
        }
      })
    }

  }
  return dataArr
}

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

//初始化
const init = () => {
  if (!pieChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(pieChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(pieChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    props.list.forEach((item: { name: string, value: number }, index: number) => {
      let value = item.value.toString().split('')
      while (value.length < 4) {
        value.unshift('0')
      }
      letters.value[index] = value
    })
    const percentage = props.list[0].value > 0 ? Math.floor(props.list[1].value / props.list[0].value * 100) : 0
    const pieAngle = props.list[0].value > 0 ? 360 * (props.list[1].value / props.list[0].value) : 9
    option.series[2].endAngle = 270 - pieAngle
    option.graphic.elements[1].style.text = percentage + "%"
    draw()
  },
  { immediate: true, deep: true }
)

const resizeChartfun = debounce(() => {
  option.graphic.elements[0].style.fontSize = offsetWidth.value / 1920 * 10
  option.graphic.elements[1].style.fontSize = offsetWidth.value / 1920 * 30
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

</script>

<style scoped lang="scss">
.wrap {
  width: 100%;
  height: 100%;
  display: flex;


  .numbers {
    flex: 1;
    padding-left: vw(8);

    display: flex;
    flex-direction: column;

    .numbers-item {
      flex: 1;

      display: flex;
      flex-direction: column;
      justify-content: space-around;

      color: #fff;

      .name {
        font-size: vh(14);
      }

      .number {
        display: flex;

        gap: vw(2);

        .letter {
          width: vw(16);
          height: vh(32);

          background-image: url("@/assets/images/cardNumber.svg");
          // background-size: 100% 100%;
          background-repeat: no-repeat;
          background-position: center;

          font-family: 'FX-LED';
          font-size: vh(28);
          font-weight: normal;
          line-height: vh(30);
          text-align: center;
          letter-spacing: 0px;

          // 文字在div中水平垂直居中
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }

  .pie {
    flex: 1;
    padding-right: vw(8);


    .pieChart {
      width: 100%;
      height: 100%;
    }
  }
}
</style>