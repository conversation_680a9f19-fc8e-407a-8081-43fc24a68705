<template>
  <div class="wrap">
    <div ref="lineChartRef" class="lineChart"></div>
  </div>
</template>
<script setup lang="ts" name="DoubleBar">
import * as echarts from 'echarts';
import type { EChartsType, EChartsOption } from 'echarts';
import { ref, onMounted, onBeforeUnmount, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { splitLineColor } = storeToRefs(dashBoardStore)

const props = withDefaults(defineProps<{ title?: string, seriesData?: any; axisData?: any; showNumber?: boolean }>(), {
  title: 'HorizontalBar',
  // axisData: ['重点行业', '纺织行业', '制造业', '采矿', '手工'],
  // seriesData: [29, 13, 23, 33, 43],
  showNumber: true
});

const lineChartRef = ref()
const chartInstance = ref<EChartsType>();

const option = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    className: 'custom-tooltip-box',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 10
    },
    formatter: `
    <div style="
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
      justify-content: space-around;"
    >
      <p style="
        overflow-wrap: break-word;
        white-space: normal;
      ">
        {b1}：
      </p>
      <p style="
        text-align:right;
        color:#18CACA;
        font-weight: bold;
      ">
        {c1}
      </p>
    </div>
    `
  },
  grid: {
    top: '10%',
    left: '2.5%',
    bottom: '5%',
    right: '5%',
    containLabel: true,
  },
  yAxis: {
    type: 'category',
    data: [],
    inverse: true,
    axisTick: {
      alignWithLabel: true,
    },
    axisLabel: {
      width:36,
      overflow: 'break',
      textStyle: {
        color: '#ddd',
        fontSize: 6,
      },
    },
  },
  xAxis: {
    name: '',
    type: 'value',
    splitLine: {
      show: false,
    },
    axisLine: {
      show: true,
      lineStyle: {
        color: '#ddd',
      }
    },
    axisLabel: {
      color: '#ddd',
      fontSize: 10,
    },
  },
  series: [
    {
      name: '底部圆',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [8, 16],
      symbolOffset: ['-50%', 0],
      z: 1,
      color: '#25C4F8',
      data: [],
    },
    {
      name: '左柱',
      type: 'bar',
      barWidth: '16',
      label: {
        show: true,
        position: 'right',
        color: '#FFFFFF',
        fontSize: 8
      },
      silent: true,
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
          {
            offset: 0,
            color: '#25C4F8',
          },
          {
            offset: 1,
            color: 'rgba(37, 196, 248, 0)',
          },
        ]),
        opacity: 1,
      },
      data: [],
      z: 2,
    },
    {
      name: '上部圆',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [8, 16],
      symbolOffset: ['-60%', 0],
      symbolPosition: 'end',
      color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
        {
          offset: 0,
          color: '#17E3E3',
        },
        {
          offset: 1,
          color: 'rgba(37, 196, 248, 0)',
        },
      ]),
      data: [],
      z: 3,
    },
  ],
};

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option as EChartsOption);
  }
};

//初始化
const init = () => {
  if (!lineChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(lineChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(lineChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option.yAxis.data = props.axisData;
    option.series[0].data = props.seriesData;
    option.series[1].data = props.seriesData;
    option.series[2].data = props.seriesData;

    // 顶部是否显示数值
    // @ts-ignore
    option.series[1].label.show = props.showNumber;

    draw()
  },
  { immediate: true, deep: true }
)

const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.lineChart {
  width: 100%;
  height: 100%;
}

.wrap {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;
}

.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>