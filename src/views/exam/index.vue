<template>
  <div class="dashboard-container">
    <Aside class="index-left" :topTitle="'体检个案数地区分布'" :centerTitle="'报告率'" :bottomTitle="'体检企业行业分布'">
      <template #top>
        <StaticProgressList :seriesData="areaCard" />
      </template>
      <template #center>
        <ExamBar3D :title="'报告率'" :axisData="['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水']"
          :seriesData="reportRate" />
      </template>
      <template #bottom>
        <HorizontalBar :axisData="industryEmpCount.axisData" :seriesData="industryEmpCount.seriesData" />
      </template>
    </Aside>
    <Center class="index-center" :title="['职业健康检查机构分布', '体检危害因素分布', '体检企业规模分布']">
      <template #top>
        <TotalNum v-for="(item, i) in total" :key="i" :title="item.name" :total="item.value"></TotalNum>
      </template>
      <template #map>
        <CenterMap></CenterMap>
      </template>
      <template #bottom>
        <OrgStatistic />
      </template>
    </Center>
    <Aside class="index-right" :topTitle="'体检个案历年趋势'" :centerTitle="'行业疑似和禁忌证检出情况'" :bottomTitle="'疑似检出情况'">
      <template #top>
        <TripleLine :title="'体检人数'" :colorIndex="1" :showYAxisSplitLine="false" :series-data="cardCountYearTrend" />
      </template>
      <template #center>
        <DoubleBarAndLineWithAPI :title="'体检企业行业'" :axisData="industryCardDiagnosis.axisData"
          :total="industryCardDiagnosis.total" :data1="industryCardDiagnosis.data1"
          :data2="industryCardDiagnosis.data2" />
      </template>
      <template #bottom>
        <DoubleBarAndLine :title="'疑似检出'" :axisData="areaCardDiagnosis.axisData"
          :total="areaCardDiagnosis.total" :data1="areaCardDiagnosis.data1"
          :data2="areaCardDiagnosis.data2" :barWidth="8" />
      </template>
    </Aside>
  </div>
</template>

<script setup lang="ts" name="dashBoard">
import { ref, watch } from "vue";
/* 布局、具体组件、数据分开 ，index中直接对整体进行布局，插入需要的组件，组件中只负责展示数据 */
// 布局组件
import Center from "@/components/layout/Center.vue";
import Aside from "@/components/layout/Aside.vue";
// 组件
import CenterMap from "./CenterMap.vue";
import TotalNum from "@/components/TotalNum.vue";
import OrgStatistic from "./OrgStatistic.vue";
// 图表组件
import HorizontalBar from "./HorizontalBar.vue";
import TripleLine from "./TripleLine.vue";
import ExamBar3D from "./ExamBar3D.vue";
import StaticProgressList from "./StaticProgressList.vue";
import DoubleBarAndLine from "./DoubleBarAndLine.vue";
import DoubleBarAndLineWithAPI from "./DoubleBarAndLineWithAPI.vue";

// pinia
import { storeToRefs } from 'pinia';
import { useExamStore } from '@/stores/exam'
const examStore = useExamStore()

const { total } = storeToRefs(examStore)
const { areaCard, reportRate, industryEmpCount } = storeToRefs(examStore)
const { cardCountYearTrend, industryCardDiagnosis, areaCardDiagnosis } = storeToRefs(examStore)

</script>

<style scoped lang="scss">
.dashboard-container {
  width: 100%;
  height: 100%;
  display: flex;

  .index-left {
    width: 25%;
  }

  .index-center {
    width: 50%;
  }

  .index-right {
    width: 25%;

    .yearSelect-btn {

      position: absolute;
      top: calc(-1.2vw * 1.33 - 4px);
      left: calc(4.44vw * 1.33 *2);

      :deep(.el-input) {
        font-size: vw(18);
      }

      :deep(.el-date-editor) {
        --el-date-editor-width: calc(4.44vw * 1.33);
        --el-input-height: calc(1.2vw * 1.33);
        // --el-date-editor-width: vh(105 * 1.2);
        // --el-input-height: vh(28 * 1.2);
      }

      :deep(.el-input__wrapper) {
        box-shadow: none;
        background-color: transparent;
        padding: 0 vw(25);

        background-image: url(@/assets/images/yearSelect.svg);
        background-repeat: no-repeat;
        background-size: auto 100%;

        cursor: pointer;
      }

      :deep(.el-input__inner) {
        width: calc(3.2vw);
        height: 1.2vw;
        line-height: 1.2vw;
        -webkit-appearance: none;
        background-color: transparent;

        border: none;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        font-size: 1em !important;
        font-weight: Medium;
        text-align: left;
        color: #18caca;
        display: inline-block;
        font-size: inherit;
        outline: 0;
        padding: 0;
        -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        cursor: pointer;

        &::placeholder {
          color: #18caca;
        }
      }

      :deep(.el-select .el-input.is-focus .el-input__wrapper) {
        box-shadow: none;
      }

      :deep(.el-input__prefix) {
        display: none;
      }

      :deep(.el-input__suffix-inner > :first-child) {
        margin-left: 0px;
      }
    }
  }
}
</style>
