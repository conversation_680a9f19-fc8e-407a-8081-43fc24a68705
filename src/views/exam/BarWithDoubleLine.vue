<template>
  <div class="wrap">
    <div class="sort">
      排序方式:<el-select v-model="sortBy" placeholder="请选择排序方式" size="small" style="width: 120px">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <div ref="lineChartRef" class="lineChart"></div>
  </div>
</template>
<script setup lang="ts" name="DoubleBar">
import * as echarts from 'echarts';
import type { EChartsType, EChartsOption } from 'echarts';
import { ref, onMounted, onBeforeUnmount, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
const { splitLineColor } = storeToRefs(dashBoardStore)


const sortBy = ref('1')
const options = ref([
  { value: '1', label: '个案卡报告数' },
  { value: '2', label: '疑似' },
  { value: '3', label: '禁忌' },
]);

const props = withDefaults(
  defineProps<{
    title?: string,
    axisData?: any;
    bar1?: number[];
    line1?: number[];
    line2?: number[];
    gap?: boolean;
    showNumber?: boolean
  }>(), {
  title: 'DoubleBarWithLine',
  // axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
  // seriesData: [290, 130, 230, 330, 430, 530, 290, 130, 230, 330, 430, 530],
  gap: true,
  showNumber: false,
  bar1: () => ([]),
  line1: () => ([]),
  line2: () => ([]),
});

const lineChartRef = ref()
const chartInstance = ref<EChartsType>();

const unit = ref(1)

const color = ['#19CCCC', '#FFD33C', '#00B5F1', '#8A7EEE', '#17BC84']
const areaColor = ['rgba(25, 204, 204, 0.33)', 'rgba(239, 199, 58, 0.33)', 'rgba(0, 181, 241, 0.39)', ' rgba(138, 126, 238, 0.39)', 'rgba(23, 188, 132, 0.39)']

function random(min: number, max: number) {
  return parseInt(Math.random() * (max - min) + min + '');
}

let xData = ['灯用电器附件及其他照明器具制造', '纺织行业', '其他未列明通用设备制造业', '金属工艺品制品', '包装装潢及其他印刷', '木地板制造', '其他未列明通用设备制造业', '汽车零部件及配件制造'],
  yData = [],
  barData = [];

for (let i = 0; i < xData.length; i++) {
  let value = random(5, 100);
  yData.push(value);
  barData.push(parseInt((value / 3 * 2) * (random(1, 100) / 100) + ''));
}

const option = {
  tooltip: {
    trigger: 'axis',

    className: 'custom-tooltip-box-barWithDoubleLine',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 10
    },
    formatter: `
    <div style="
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
      justify-content: space-around;"
    >
      <p>{b}</p>
      <p>{a0}：<span style="color:#00B5F1;font-weight:bold;">{c1}</span></p>
      <p>{a3}：<span style="color:#19CCCC;font-weight:bold;">{c3}</span></p>
      <p>{a4}：<span style="color:#FAC858;font-weight:bold;">{c4}</span></p>
    </div>
    `},
  legend: [
    {
      data: [
        { name: '个案卡数', itemStyle: { color: '#00B5F1' } },
      ],
      textStyle: {
        color: '#ccc'
      },
      icon: 'roundRect',
      itemHeight: 8,
      itemWidth: 10,
      left: '10%',
    },
    {
      data: [
        { name: '疑似率', itemStyle: { color: color[0] } },
        { name: '禁忌率', itemStyle: { color: color[1] } },
      ],
      textStyle: {
        color: '#ccc'
      },
      icon: 'rect',
      itemHeight: 2,
      itemWidth: 16,
      right: '10%',
    }
  ],
  grid: {
    top: '20%',
    left: '2.5%',
    bottom: '5%',
    right: '2.5%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    axisTick: {
      alignWithLabel: true,
    },
    axisLabel: {
      textStyle: {
        color: '#ddd',
        fontSize: 6,
      },
      width: 30,
      overflow: 'break',
      interval: 0,
    },
    data: xData,
  },
  yAxis: [
    {
      name: '',
      nameTextStyle: {
        padding: [0, 20, 0, 0]
      },
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: splitLineColor.value,
        },
      },
      axisLabel: {
        color: '#D9D9D9',
        fontSize: 12,
      },
      position: "left",
    },
    {
      name: '%',
      nameTextStyle: {
        padding: [0, 0, 0, 20]
      },
      type: 'value',
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: '#D9D9D9',
        fontSize: 12,
      },
      position: "right"
    }
  ],
  series: [
    // 个案卡数
    {
      name: '个案卡数',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [16, 8],
      symbolOffset: ['0', 5],
      color: '#00B5F1',
      data: yData,
      yAxisIndex: 0,
      z: 1,
    },
    {
      name: '个案卡数',
      type: 'bar',
      barWidth: '16',
      barGap: '20%',
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 10,
        fontFamily: 'DS-Digital'
      },
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(0, 181, 241, 0)',
          },
          {
            offset: 1,
            color: '#25C4F8',
          },
        ]),
        opacity: 1,
      },
      data: yData.map((item) => item + 20),
      yAxisIndex: 0,
      z: 2,
    },
    {
      name: '个案卡数',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [16, 8],
      symbolOffset: ['0', -4],
      symbolPosition: 'end',
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(0, 181, 241, 0)',
        },
        {
          offset: 1,
          color: '#00B5F1',
        },
      ]),
      data: yData.map((item) => item + 20),
      yAxisIndex: 0,
      z: 3,
    },

    {
      yAxisIndex: 1,
      name: '疑似率',
      type: 'line',
      smooth: true,
      data: [],
      symbolSize: 0,
      label: { show: false },

      lineStyle: { width: 3, color: color[0] },
      areaStyle: { //区域填充样式
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(25, 204, 204, 0.6727)'
        },
        {
          offset: 1,
          color: 'rgba(25, 204, 204, 0.11)'
        }
        ], false),
      },
    },
    {
      yAxisIndex: 1,
      name: '禁忌率',
      type: 'line',
      smooth: true,
      data: [],
      symbolSize: 0,
      label: { show: false },
      lineStyle: { width: 3, color: color[1] },
      areaStyle: { //区域填充样式
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(239, 199, 58, 0.6727)',
        },
        {
          offset: 1,
          color: 'rgba(252, 227, 209, 0.2141)',
        }
        ], false),
      },
    },
  ],
};

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option as EChartsOption);
  }
};

//初始化
const init = () => {
  if (!lineChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(lineChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(lineChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    // option.xAxis[0].data = props.axisData;  // x轴数据
    // option.series[0].data[0].value = props.data;
    // option.series[1].data[0].value = props.data;

    // 顶部是否显示数值
    // @ts-ignore
    option.series[1].label.show = props.showNumber;

    option.series[3].data = yData.map((item) => item + parseInt(Math.random() * 20 - Math.random() * 20));
    option.series[4].data = yData.map((item) => item + 20);

    draw()
  },
  { immediate: true, deep: true }
)

const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.lineChart {
  width: 100%;
  height: 100%;
}

.wrap {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  .sort {
    font-size: 14px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0px;

    color: #44FFFF;
    text-shadow: 0px 0px 9px rgba(86, 254, 254, 0.53);

    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: vh(2.5) vw(5) vh(2.5) 0;
  }
}

:deep(.el-select__wrapper) {
  background-image: url(../../assets/images/selectBg.svg) !important;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-color: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;

}

:deep(.el-select__selected-item) {
  color: #44FFFF;
  text-shadow: 0px 0px 9px rgba(86, 254, 254, 0.53);
}

:deep(.el-select__suffix .el-icon) {
  color: #44FFFF;
  text-shadow: 0px 0px 9px rgba(86, 254, 254, 0.53);
}
</style>

<style lang="scss">
.custom-tooltip-box-barWithDoubleLine {
  width: calc(117.36px * 1.4);
  height: calc(55px * 1.4);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // background-color: black;
    // opacity: 0.2;
    z-index: 1;
  }
}
</style>