<template>
  <div class="list-container">
    <div v-if="listData.length > 0" class="scroll-wrap">
      <div v-for="(item, index) in listData" :key="index">
        <div class="list-item">
          <el-tooltip v-if="props.titleShrink" class="box-item" effect="dark" placement="top" :append-to-body="true"
            popper-class="custom-tooltip" :teleported="false">
            <span class="list-item-name">{{ item.name }}</span>
            <template #content>
              <div style="width: 10vw;text-align: center;">{{ item.name }}</div>
            </template>
          </el-tooltip>
          <span v-else style="margin-left: 0.5em;">{{ item.name }}</span>
          <el-progress :stroke-width="8" :percentage="item.value == 0 ? 0 : Math.round(item.value / max * 100)"
            style="flex:1;position: relative;left: 6px;">
            <span class="value" :style="{ width: props.numberWidth + 'px' }">{{ item.value }}</span>
          </el-progress>
        </div>
      </div>
    </div>
    <div v-else class="scroll-wrap">
      <div class="list-item" style="display: flex;justify-content: center;">
        <span>暂无数据</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup name="ProgressList">
import { ref, watchEffect } from "vue";

type SeriesData = {
  name: string;
  value: number;
};

const props = withDefaults(defineProps<{ seriesData: SeriesData[]; titleShrink?: boolean; numberWidth?: number }>(), {
  titleShrink: false, // 标题是否缩略 默认false
  numberWidth: 42, // 数值宽度 默认42
})

const max = ref(0);
const listData = ref()

watchEffect(() => {
  // 深拷贝props.seriesData
  const data: SeriesData[] = JSON.parse(JSON.stringify(props.seriesData));
  listData.value = data;
  // listData.value = data.sort((a: SeriesData, b: SeriesData) => b.value - a.value);
  listData.value.length && (max.value = listData.value[0].value)
})

</script>

<style lang="scss" scoped>
.list-container {
  width: 100%;
  height: 100%;
  padding: 0 vw(10);
  display: flex;
  flex-direction: column;

  // .list-header {
  //   width: 100%;
  //   font-size: 1em;
  //   height: 2.25em;
  //   line-height: 2.25em;
  //   background-color: rgba(15, 128, 131, 1);
  //   color: #FFFFFF;
  //   border-radius: 0.5em;
  //   padding: 0 0.66em;
  //   display: flex;

  //   justify-content: space-between;
  //   margin-top: vh(9);
  //   margin-bottom: vh(11);
  // }

  .scroll-wrap {
    width: 100%;
    height: vh(203);
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;

    .list-item {
      width: 100%;
      font-size: vh(8);
      height: vh(8);
      line-height: vh(8);
      color: #FFFFFF;
      margin-top: vh(2);

      display: flex;

      .list-item-name {
        // 最多显示4个字，超过显示省略号，鼠标悬浮显示全部
        width: 18%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .value {
        display: flex;
      }
    }
  }

  :deep(.el-progress-bar__inner) {
    border-radius: 0 0 0 0;
    background: linear-gradient(270deg, rgba(24, 202, 202, 0.61) 45%, rgba(51, 142, 215, 0) 100%) !important;
    transform: skewX(-20deg) translateX(-3px);
  }

  :deep(.el-progress-bar__inner::after) {
    width: 4px;
    height: 12px;
    background: #18CACA;
    box-shadow: -3px 0px 4.5px 0px #8AF0F0;
  }

  :deep(.el-progress__text) {
    color: #FFFFFF;
    font-size: 11px !important;
    font-weight: normal !important;
    min-width: 42px;
  }

  :deep(.el-progress-bar__outer) {
    border-radius: 0;
    background-color: #091621 !important;
  }
}

.textE {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.custom-tooltip {
  z-index: 9999 !important;
}
</style>