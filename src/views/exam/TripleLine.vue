<template>
  <div class="rank-container">
    <div ref="barChartRef" class="barChart"></div>
  </div>
</template>
<script setup lang="ts" name="RegionalRank">
import * as echarts from 'echarts';
import { ref, onMounted, onBeforeUnmount, watch, markRaw, nextTick } from "vue";
import { emitter } from "@/utils/mitt";
import { debounce } from 'lodash';
import { getUnitShrink } from '@/utils/tools'

const props = withDefaults(defineProps<{
  title?: string,
  axisData?: any, // x轴数据
  seriesData?: any,
  showYAxisSplitLine?: boolean, // 是否显示y轴
  showSymbol?: boolean, // 是否显示symbol标记点(以及label数字)
  yAxisName?: string, // y轴名称
  yAxisLabelShrink?: boolean; // y轴标签是否缩小
}>(),
  {
    title: 'DoubleLineChart',
    axisData: ['2017', '2018', '2019', '2020', '2021', '2022', '2023', '2024'],
    showYAxisSplitLine: true,
    showSymbol: false,
    yAxisName: '',
    yAxisLabelShrink: true,
  })

const unit = ref(1)
// default,yellow,bule,purple,green
const color = ['#19CCCC', '#FFD33C', '#00B5F1', '#8A7EEE', '#17BC84']
const areaColor = ['rgba(25, 204, 204, 0.33)', 'rgba(239, 199, 58, 0.33)', 'rgba(0, 181, 241, 0.39)', ' rgba(138, 126, 238, 0.39)', 'rgba(23, 188, 132, 0.39)']

const getOption = () => {
  return {
    color: color.slice(0, 3),
    legend: {
      textStyle: {
        color: '#ccc'
      },
      icon: 'rect',
      itemHeight: 2,
      itemWidth: 16,
      right: '12.5%',
    },
    tooltip: {
      trigger: 'axis',
      className: 'custom-tooltip-box-doubleLine',
      // 清除默认样式
      padding: 0,
      borderWidth: 0,
      borderColor: "transparent",
      backgroundColor: "transparent",
      textStyle: {
        color: "#FFFFFF",
        fontSize: 8
      },
    },
    grid: {
      top: '22.5%',
      left: '5%',
      right: '4%',
      bottom: '-0',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: '#D9D9D9',
        fontSize: 10,
        interval: 0,
      },
    },
    yAxis: [
      {
        show: true,
        name: '',
        nameTextStyle: {
          padding: [0, 30, 0, 0]
        },
        type: 'value',
        boundaryGap: [0, 0.01],
        max: (value: { min: number, max: number; }) => {  // 百位起最大值向上取整
          if (value.max <= 10) {
            return 10;
          }
          else return undefined;
        },
        // 分隔线
        splitLine: {
          show: props.showYAxisSplitLine,
          lineStyle: {
            color: '#D9D9D9',
          },
        },
        // 标签文字颜色
        axisLabel: {
          color: '#D9D9D9',
          fontSize: 10,
          formatter(value: number) {
            if (value) {
              // 返回100以内的整数
              return Math.floor(value / unit.value)
            }
          },
        },
      },
      {
        name: '%',
        nameTextStyle: {
          padding: [0, 0, 0, 30]
        },
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#D9D9D9',
          fontSize: 12,
        },
        position: "right"
      }
    ],
    series: [
      {
        name: '禁忌率',
        type: 'line',
        smooth: true,
        data: [],
        yAxisIndex: 1,
        symbolSize: 10,
        lineStyle: { width: 3, color: color[0] },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 6,
        },
        areaStyle: { //区域填充样式
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            // color: color[0]
            color: 'rgba(25, 204, 204, 0.6727)'
          },
          {
            offset: 1,
            // color: areaColor[0]
            color: 'rgba(25, 204, 204, 0.11)'
          }
          ], false),
        },
      },
      {
        name: '疑似率',
        type: 'line',
        smooth: true,
        data: [],
        yAxisIndex: 1,
        symbolSize: 10,
        lineStyle: { width: 3, color: color[1] },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 6,
        },
        areaStyle: { //区域填充样式
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgba(239, 199, 58, 0.6727)',
          },
          {
            offset: 1,
            color: 'rgba(252, 227, 209, 0.2141)',
          }
          ], false),
        },
      },
      {
        name: '个案卡数',
        type: 'line',
        smooth: true,
        data: [],
        symbolSize: 10,
        lineStyle: { width: 3, color: color[2] },
        label: {
          show: true,
          position: 'top',
          color: '#fff',
          fontSize: 6,
        },
        areaStyle: { //区域填充样式
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
            offset: 0,
            color: 'rgba(0, 181, 241, 0.6727)',
          },
          {
            offset: 1,
            color: 'rgba(0, 181, 241, 0.2141)',
          }
          ], false),
        },
      },

    ]
  }
}
let option = getOption()

const barChartRef = ref()
const chartInstance = ref();

// 绘制(更新)
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// 初始化
const initBar = () => {
  if (!barChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(barChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(barChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
}

// resize
const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value.resize();
    draw()
  });
}, 500)

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    option = getOption()
    // cardCountYearTrend
    const data = JSON.parse(JSON.stringify(props.seriesData))
    const cardCountData = data.map((item: any) => { return item.cardCount })
    option.xAxis.data = data.map((item: any) => { return item.year })
    option.series[0].data = data.map((item: any) => {
      return item.cardCount > 0 ? (item.contraindicationCount / item.cardCount * 100).toFixed(2) : 0
    })
    option.series[1].data = data.map((item: any) => {
      return item.cardCount > 0 ? (item.suspectedCount / item.cardCount * 100).toFixed(2) : 0
    })
    option.series[2].data = cardCountData
    // showSymbol
    option.series[0].symbolSize = props.showSymbol ? 10 : 0
    option.series[0].label.show = props.showSymbol
    option.series[1].symbolSize = props.showSymbol ? 10 : 0
    option.series[1].label.show = props.showSymbol
    option.series[2].symbolSize = props.showSymbol ? 10 : 0
    option.series[2].label.show = props.showSymbol

    option.yAxis[0].name = props.yAxisName
    if (props.yAxisLabelShrink) {
      const res = getUnitShrink(cardCountData)
      option.yAxis[0].name = res.unitName + props.yAxisName
      unit.value = res.unit
    }

    draw()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  initBar()
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
})

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});
</script>

<style scoped lang="scss">
.rank-container {
  width: 100%;
  height: 100%;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  @media screen and (max-width: 1960px) {
    font-size: 10px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 8px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 6px;
  }

  .barChart {
    width: 100%;
    height: 100%;
  }
}
</style>

<style lang="scss">
.custom-tooltip-box-doubleLine {
  width: calc(117.36px * 1.6);
  height: calc(55px * 1.6);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 12px !important;
  padding-right: 16px !important;
  display: none; // 防止首次渲染时出现

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    // background-color: black;
    // opacity: 0.2;
    z-index: 1;
  }
}
</style>