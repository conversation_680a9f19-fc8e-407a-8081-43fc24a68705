<template>
  <div class="rank-container">
    <div ref="barChartRef" class="barChart"></div>

    <div v-show="false">
      <div id="bar3DTooltip" style="
        color: #FFFFFF;
        font-size: 12;
        display: flex;
        flex-direction: column;
        height: 100%;
        width: 100%;
        justify-content: space-around;
        ">
        <p>{{ props.seriesData[dataIndex].name || props.title }}</p>
        <p>个案卡数：<span style="color:#17E3E3;font-weight:bold;">{{ props.seriesData[dataIndex].cardCount || 0 }}</span>
        </p>
        <p>交换成功数：<span style="color:#17E3E3;font-weight:bold;">{{ props.seriesData[dataIndex].examCount || 0 }}</span>
        </p>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="RegionalRank">
import * as echarts from 'echarts';
import { ref, onMounted, onBeforeUnmount, watch, markRaw, nextTick } from "vue";
import { emitter } from "@/utils/mitt";
import { debounce } from 'lodash';

const props = withDefaults(defineProps<{ title?: string, axisData?: any, seriesData?: any, colorIndex?: number, yAxisName?: string }>(),
  {
    title: 'BarChart',
    // axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
    // seriesData: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
    colorIndex: 0,
    yAxisName: ''
  })

const color = ['#19CCCC', '#FFD33C', '#00B5F1', '#8A7EEE', '#17BC84']

const dataIndex = ref(0)

const option = {
  // color: '#01C4D3',
  color: color[props.colorIndex],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    className: 'custom-tooltip-box',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 12
    },
    formatter: (params) => {
      dataIndex.value = params[0].dataIndex
      return document.getElementById("bar3DTooltip")
    }
  },
  grid: {
    top: '20%',
    left: '3%',
    right: '4%',
    bottom: '-0',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      alignWithLabel: true, // true：标签位于刻度线正下方；false：标签位于2个刻度线中间
    },
    axisLabel: {
      color: '#ddd',
      fontSize: 8,
      interval: 0,
    },
  },
  yAxis: {
    name: '',
    type: 'value',
    boundaryGap: [0, 0.01],
    max: (value: { min: number, max: number; }) => {  // 数据值很小时，设置最大值为10
      if (value.max <= 10) {
        return 10;
      }
      else return undefined;
    },
    // 分隔线
    splitLine: {
      lineStyle: {
        type: 'dashed',
        // color: '#D9D9D9',
        color: '#333'
      },
    },
    // 标签文字颜色
    axisLabel: {
      color: '#D9D9D9',
      fontSize: 10,
    },
  },

  series: [
    // 底部圆
    {
      data: [],
      type: 'pictorialBar',
      symbol: 'diamond',
      symbolOffset: [0, '50%'],
      symbolSize: [16, 8],
      itemStyle: {
        color: {
          x: 0, y: 0, x2: 1, y2: 0,
          type: 'linear',
          colorStops: [
            {
              offset: 0,
              color: '#108085'
            },
            {
              offset: 0.5,
              color: '#108085'
            },
            {
              offset: 0.5,
              color: '#18CACA'
            },
            {
              offset: 1,
              color: '#18CACA'
            }
          ]
        }
      }
    },
    {
      // bar
      name: props.title,
      data: [],
      type: 'bar',
      barWidth: 16,
      itemStyle: {
        color: {
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          type: 'linear',
          global: false,
          colorStops: [
            {
              offset: 0,
              color: '#108085'
            },
            {
              offset: 0.5,
              color: '#108085'
            },
            {
              offset: 0.5,
              color: '#18CACA'
            },
            {
              offset: 1,
              color: '#18CACA'
            }
          ]
        }
      },
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 8
      }
    },

    // 顶部圆
    {
      data: [],
      type: 'pictorialBar',
      symbolPosition: 'end',
      symbol: 'diamond',
      symbolOffset: [0, '-50%'],
      symbolSize: [16, 8],
      zlevel: 2,
      color: '#44FFFF'
    }
  ],
}

const barChartRef = ref()
const chartInstance = ref();

// 绘制(更新)
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// 初始化
const initBar = () => {
  if (!barChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(barChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(barChartRef.value, undefined, { renderer: "svg" }));
    draw();
  }
}

// resize
const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value.resize();
    draw()
  });
}, 500)

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    const data = JSON.parse(JSON.stringify(props.seriesData))
    const axisData = data.map((item: any) => {
      return item.name
    })
    const seriesData = data.map((item: any) => {
      return item.rate
    })
    option.xAxis.data = axisData
    option.series[0].data = seriesData.map((item, i) => {
      return i === 0 ? { symbol: 'circle', itemStyle: { color: '#FFD33C', }, value: 1, } : 1
    })
    option.series[1].data = seriesData.map((item, i) => {
      return i === 0 ? {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(255, 211, 60, 0.46)', }, { offset: 1, color: '#FFD33C', },]),
          opacity: 1,
        },
        value: item,
      } : item
    })
    option.series[2].data = seriesData.map((item, i) => {
      return i === 0 ? {
        symbol: 'circle',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{ offset: 0, color: 'rgba(255, 211, 60, 0.46)', }, { offset: 1, color: '#FFD33C', },]),
          opacity: 1,
        },
        value: item,
      } : item
    })

    draw()
  },
  { immediate: true, deep: true }
)

onMounted(() => {
  initBar()
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
})

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.rank-container {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  @media screen and (max-width: 1960px) {
    font-size: 10px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 8px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 6px;
  }

  .barChart {
    width: 100%;
    height: 100%;
  }
}

.custom-tooltip-box {
  width: calc(117.36px * 1.2);
  height: calc(55px * 1.2);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现
  // backdrop-filter: blur(2px);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: black;
    opacity: 0.2;
    z-index: 1;
  }
}
</style>
