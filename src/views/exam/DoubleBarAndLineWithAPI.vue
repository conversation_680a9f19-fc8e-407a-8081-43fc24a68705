<template>
  <div class="wrap">
    <div class="sort">
      排序方式:<el-select v-model="sortBy" placeholder="请选择排序方式" size="small" style="width: 120px" :teleported="false"
        @change="sortChange">
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </div>
    <div ref="lineChartRef" class="lineChart"></div>
  </div>
</template>
<script setup lang="ts" name="DoubleBar">
import * as echarts from 'echarts';
import type { EChartsType, EChartsOption } from 'echarts';
import { ref, onMounted, onBeforeUnmount, markRaw, watch, nextTick } from "vue";
import { emitter } from '@/utils/mitt';
import { debounce } from 'lodash'
import { getUnitShrink } from '@/utils/tools'

// pinia
import { storeToRefs } from 'pinia';
import { useDashBoardStore } from '@/stores';
const dashBoardStore = useDashBoardStore();
import { useExamStore } from '@/stores/exam'
const examStore = useExamStore()
const { splitLineColor, year, code } = storeToRefs(dashBoardStore)


const sortBy = ref('cardCount')
const options = ref([
  { value: 'cardCount', label: '个案卡报告数' },
  { value: 'suspectedCount', label: '疑似卡报告数' },
  { value: 'contraindicationCount', label: '禁忌卡报告数' },
]);

const sortChange = (value: string) => {
  examStore.getIndustryCardDiagnosisData({ year: year.value, code: code.value, orderBy: value }) // 个案卡行业疑似和禁忌检出情况
}

const tooltipContent = (params) => {
  const i = params[0].dataIndex
  return `
    <div style="
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
      justify-content: space-around;"
    >
      <p>${params[0].axisValue}</p>
      <p>体检个案数：<span style="color:#17E3E3;font-weight:bold;">${props.total[i]}</span></p>
      <p>疑似数：<span style="color:#17E3E3;font-weight:bold;">${props.data1[i]}</span></p>
      <p>禁忌数：<span style="color:#FFD33C;font-weight:bold;">${props.data2[i]}</span></p>
      <p>疑似率：<span style="color:#17E3E3;font-weight:bold;">${props.total[i] > 0 ? (props.data1[i] / props.total[i] * 100).toFixed(2) : 0}%</span></p>
      <p>禁忌率：<span style="color:#FFD33C;font-weight:bold;">${props.total[i] > 0 ? (props.data2[i] / props.total[i] * 100).toFixed(2) : 0}%</span></p>
    </div>
    `
}

const props = withDefaults(
  defineProps<{
    title?: string,
    axisData?: any;
    total: number[];
    data1: number[];
    data2: number[];
    gap?: boolean;
    showNumber?: boolean;
    barWidth?: number;
    xAxisLabelShrink?: boolean;
    yAxisLabelShrink?: boolean;
    yAxisName?: string;
  }>(), {
  title: 'DoubleBarAndLine',
  // axisData: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水'],
  gap: true,
  showNumber: false,
  total: () => ([]),
  data1: () => ([]),
  data2: () => ([]),
  barWidth: 16,
  xAxisLabelShrink: false,
  yAxisLabelShrink: true,
  yAxisName: '',
});

const lineChartRef = ref()
const chartInstance = ref<EChartsType>();

const unit = ref(1)

const color = ['#19CCCC', '#FFD33C', '#00B5F1', '#8A7EEE', '#17BC84']
const areaColor = ['rgba(25, 204, 204, 0.33)', 'rgba(239, 199, 58, 0.33)', 'rgba(0, 181, 241, 0.39)', ' rgba(138, 126, 238, 0.39)', 'rgba(23, 188, 132, 0.39)']

const option = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    className: 'custom-tooltip-box-DBAL',
    // 清除默认样式
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 8
    },
    formatter: tooltipContent
  },
  legend: [
    {
      data: [
        { name: '疑似数', itemStyle: { color: color[0] } },
        { name: '禁忌数', itemStyle: { color: color[1] } },
      ],
      textStyle: {
        color: '#ccc'
      },
      icon: 'roundRect',
      itemHeight: 8,
      itemWidth: 10,
      left: '10%',
    },
    {
      data: [
        { name: '疑似率', itemStyle: { color: color[0] } },
        { name: '禁忌率', itemStyle: { color: color[1] } },
      ],
      textStyle: {
        color: '#ccc'
      },
      icon: 'rect',
      itemHeight: 2,
      itemWidth: 16,
      right: '10%',
    }
  ],
  grid: {
    top: '20%',
    left: '2.5%',
    bottom: '5%',
    right: '2.5%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: [],
    axisTick: {
      alignWithLabel: true,
    },
    axisLabel: {
      textStyle: {
        color: '#ddd',
        fontSize: 8,
      },
      interval: 0,
      width: 36,
      overflow: 'break',
    },
  },
  yAxis: [
    {
      name: '',
      nameTextStyle: {
        padding: [0, 20, 0, 0]
      },
      type: 'value',
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: splitLineColor.value,
        },
      },
      axisLabel: {
        color: '#D9D9D9',
        fontSize: 10,
        formatter(value: number) {
          if (value) {
            // 返回100以内的整数
            return Math.floor(value / unit.value)
          }
        },
      },
      position: "left",
    },
    {
      name: '%',
      nameTextStyle: {
        padding: [0, 0, 0, 20]
      },
      type: 'value',
      splitLine: {
        show: false,
      },
      axisLabel: {
        show: true,
        color: '#D9D9D9',
        fontSize: 12,
      },
      position: "right"
    }
  ],
  series: [
    // 疑似数
    {
      name: '疑似数',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [props.barWidth, props.barWidth * 0.5],
      symbolOffset: ['-60%', props.barWidth * 0.25],
      color: '#25C4F8',
      data: [],
      yAxisIndex: 0,
      z: 1,
    },
    {
      name: '疑似数',
      type: 'bar',
      barWidth: props.barWidth,
      barGap: '20%',
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 10,
        fontFamily: 'DS-Digital'
      },
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(37, 196, 248, 0)',
          },
          {
            offset: 1,
            color: '#25C4F8',
          },
        ]),
        opacity: 1,
      },
      data: [],
      yAxisIndex: 0,
      z: 2,
    },
    {
      name: '疑似数',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [props.barWidth, props.barWidth * 0.5],
      symbolOffset: ['-60%', -props.barWidth * 0.25],
      symbolPosition: 'end',
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(37, 196, 248, 0)',
        },
        {
          offset: 1,
          color: '#17E3E3',
        },
      ]),
      //  linear-gradient(0deg, #17E3E3 0%, rgba(37, 196, 248, 0) 100%);
      data: [],
      yAxisIndex: 0,
      z: 3,
    },
    // 禁忌数
    {
      name: '禁忌数',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [props.barWidth, props.barWidth * 0.5],
      symbolOffset: ['60%', props.barWidth * 0.25],
      color: '#FFD33C',
      data: [],
      yAxisIndex: 0,
      z: 1,
    },
    {
      name: '禁忌数',
      type: 'bar',
      barWidth: props.barWidth,
      label: {
        show: true,
        position: 'top',
        color: '#FFFFFF',
        fontSize: 10,
        fontFamily: 'DS-Digital'
      },
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: 'rgba(248, 211, 3, 0)',
          },
          {
            offset: 1,
            color: '#FFD33C',
          },
        ]),
        opacity: 1,
      },
      data: [],
      yAxisIndex: 0,
      z: 2,
    },
    {
      name: '禁忌数',
      type: 'pictorialBar',
      silent: true,
      symbolSize: [props.barWidth, props.barWidth * 0.5],
      symbolOffset: ['60%', -props.barWidth * 0.25],
      symbolPosition: 'end',
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: 'rgba(248, 211, 3, 0)',
        },
        {
          offset: 1,
          color: '#FFD33C',
        },
      ]),
      data: [],
      yAxisIndex: 0,
      z: 3,
    },
    {
      yAxisIndex: 1,
      name: '疑似率',
      type: 'line',
      smooth: true,
      data: [],
      symbolSize: 0,
      label: { show: false },

      lineStyle: { width: 3, color: color[0] },
      areaStyle: { //区域填充样式
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(25, 204, 204, 0.6727)'
        },
        {
          offset: 1,
          color: 'rgba(25, 204, 204, 0.11)'
        }
        ], false),
      },
    },
    {
      yAxisIndex: 1,
      name: '禁忌率',
      type: 'line',
      smooth: true,
      data: [],
      symbolSize: 0,
      label: { show: false },
      lineStyle: { width: 3, color: color[1] },
      areaStyle: { //区域填充样式
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [{
          offset: 0,
          color: 'rgba(239, 199, 58, 0.6727)',
        },
        {
          offset: 1,
          color: 'rgba(252, 227, 209, 0.2141)',
        }
        ], false),
      },
    },
  ],
};

// 绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option as EChartsOption);
  }
};

//初始化
const init = () => {
  if (!lineChartRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(lineChartRef.value);

  if (!chartInstance.value) {
    chartInstance.value = markRaw(echarts.init(lineChartRef.value, undefined, { renderer: "svg" }));
    draw();
    chartInstance.value.on('legendselectchanged', function (params) {
      var selected = params.selected; // 获取当前图例选中状态的对象
      var name = params.name; // 获取被点击的图例名称
      if (name.includes('数')) {
        if (selected['疑似数'] && selected['禁忌数']) {
          option.series[0].symbolOffset = props.gap ? ['-60%', '50%'] : ['0%', '50%'];
          option.series[2].symbolOffset = props.gap ? ['-60%', '-50%'] : ['0%', '-50%'];
          option.series[3].symbolOffset = props.gap ? ['60%', '50%'] : ['0%', '50%'];
          option.series[5].symbolOffset = props.gap ? ['60%', '-50%'] : ['0%', '-50%'];
        } else {
          option.series[0].symbolOffset[0] = '0%';
          option.series[2].symbolOffset[0] = '0%';
          option.series[3].symbolOffset[0] = '0%';
          option.series[5].symbolOffset[0] = '0%';
        }
        draw()
      }
    });
  }
};

// 监听props，处理父组件传递的数据
watch(
  props,
  () => {
    // 双柱是否重叠
    option.series[0].symbolOffset = props.gap ? ['-60%', '50%'] : ['0%', '50%'];
    option.series[1].barGap = props.gap ? '20%' : '-100%';
    option.series[2].symbolOffset = props.gap ? ['-60%', '-50%'] : ['0%', '-50%'];
    option.series[3].symbolOffset = props.gap ? ['60%', '50%'] : ['0%', '50%'];
    option.series[4].barGap = props.gap ? '20%' : '-100%';
    option.series[5].symbolOffset = props.gap ? ['60%', '-50%'] : ['0%', '-50%'];
    // 顶部是否显示数值
    // @ts-ignore
    option.series[1].label.show = props.showNumber;
    // @ts-ignore
    option.series[4].label.show = props.showNumber;

    option.yAxis[0].name = props.yAxisName
    if (props.yAxisLabelShrink) {
      const max1 = Math.max(...props.data1)
      const max2 = Math.max(...props.data2)
      const res = getUnitShrink(max1 > max2 ? props.data1 : props.data2)
      option.yAxis[0].name = res.unitName + props.yAxisName
      unit.value = res.unit
    }

    option.xAxis.data = props.axisData;  // x轴数据
    // 根据total的length填充option.series[1].data
    option.series[0].data = new Array(props.axisData.length).fill(1);  // 疑似数
    option.series[1].data = props.data1;
    option.series[2].data = props.data1;
    option.series[3].data = new Array(props.axisData.length).fill(1);  // 疑似数
    option.series[4].data = props.data2;
    option.series[5].data = props.data2;
    option.series[6].data = props.data1.map((item: number, i: number) => {
      return props.total[i] > 0 ? (item / props.total[i] * 100).toFixed(2) : 0
    })
    option.series[7].data = props.data2.map((item: number, i: number) => props.total[i] > 0 ? (item / props.total[i] * 100).toFixed(2) : 0)

    draw()
  },
  { immediate: true, deep: true }
)

const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw()
  });
}, 500)

onMounted(() => {
  init();
  resizeChartfun()
  emitter.on('resize', resizeChartfun)
});

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});

</script>

<style scoped lang="scss">
.lineChart {
  width: 100%;
  height: 100%;
}

.wrap {
  width: 100%;
  height: 100%;
  // width: 11vw;
  // height: 30vh;

  display: flex;
  flex-direction: column;
  justify-content: center;

  font-size: 12px;

  .sort {
    font-size: 14px;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 0px;

    color: #44FFFF;
    text-shadow: 0px 0px 9px rgba(86, 254, 254, 0.53);

    display: flex;
    justify-content: flex-end;
    align-items: center;
    margin: vh(2.5) vw(5) vh(2.5) 0;
  }
}

:deep(.el-select__wrapper) {
  background-image: url(../../assets/images/selectBg.svg) !important;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-color: transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;

}

:deep(.el-select__selected-item) {
  color: #44FFFF;
  text-shadow: 0px 0px 9px rgba(86, 254, 254, 0.53);
}

:deep(.el-select__suffix .el-icon) {
  color: #44FFFF;
  text-shadow: 0px 0px 9px rgba(86, 254, 254, 0.53);
}
</style>

<style lang="scss">
.custom-tooltip-box-DBAL {
  width: calc(117.36px * 1.6);
  height: calc(55px * 1.6);
  background-image: url(../../assets/images/toolTipBg.svg);
  background-size: 100% 100%;
  background-repeat: no-repeat;
  padding: 10px !important;
  display: none; // 防止首次渲染时出现

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
  }
}
</style>