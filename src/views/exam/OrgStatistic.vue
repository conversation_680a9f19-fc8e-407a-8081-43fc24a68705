<template>
    <div class="OrgStatistic-outerContainer">
        <div class="statisticItem">
            <scatter :title="'职业健康检查机构数'" :axisData="examOrgArea.axisData" :seriesData="examOrgArea.seriesData"></scatter>
        </div>
        <div class="statisticItem">
            <Radar :axisData="hazardCardCount.axisData" :seriesData="hazardCardCount.seriesData">
            </Radar>
        </div>
        <div class="statisticItem">
            <Pie3D :seriesData="IndustryEnterpriseSize"></Pie3D>
        </div>
    </div>
</template>


<script setup lang="ts" name="OrgStatistic">
import Pie3D from "@/components/charts/Pie3D.vue";
import Radar from "@/components/charts/Radar.vue";
import scatter from "@/components/charts/scatter.vue";
import { ref } from 'vue'
// pinia
import { storeToRefs } from 'pinia';
import { useExamStore } from '@/stores/exam'
const examStore = useExamStore()

const { examOrgArea, hazardCardCount, IndustryEnterpriseSize } = storeToRefs(examStore)

</script>

<style scoped lang="scss">
.OrgStatistic-outerContainer {
    width: 100%;
    height: 100%;
    display: flex;

    .statisticItem {
        width: calc(100% /3);
        height: 100%;
    }
}
</style>