<template>
  <div class="container" :class="{ right: props.right }">
    <div class="title">{{ props.title }}</div>
    <div class="rate1">{{ props.rateName1 }}<span class="number">{{ props.rateValue1 }}</span></div>
    <div class="rate2">{{ props.rateName2 }}<span class="number">{{ props.rateValue2 }}</span></div>
  </div>
</template>

<script setup lang="ts" name="Rate">
const props = withDefaults(defineProps<{
  right?: boolean
  title?: string
  rateName1?: string
  rateValue1?: string
  rateName2?: string
  rateValue2?: string
}>(), {
  right: false,
  title: '省级监督系统',
  rateName1: '交换率：',
  rateValue1: '100%',
  rateName2: '成功率：',
  rateValue2: '100%',
})

</script>

<style scoped lang="scss">
.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: #fff;
  z-index: 9;

  &.right {
    align-items: flex-end
    }

  .title {
    font-size: vh(14);

    letter-spacing: 0.08px;
  }

  .rate1 {
    font-size: vh(15);
    font-weight: bold;
    line-height: normal;
    letter-spacing: 0.08px;
    font-family: DS-Digital;

    .number {
      font-weight: normal;
      font-size: vh(30);
    }
  }

  .rate2 {
    margin-top: vh(10);
    height: vh(26);
    background-image: url(@/assets/images/rateBg.svg);
    background-size: vh(107) 100%;
    background-repeat: no-repeat;

    font-size: vh(14);
    font-weight: 400;
    font-family: DS-Digital;


    display: flex;
    align-items: center;
    padding-left: vh(30);

    .number {
      font-size: vh(25);
    }
  }
}
</style>