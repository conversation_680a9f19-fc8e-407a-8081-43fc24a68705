<template>
  <div class="centerMap">
    <div class="centerMap-content">
      <!-- echarts地图 -->
      <div id="map" ref="mapRef" class="map"></div>
      <!-- 年份选择器 -->
      <div class="yearSelect-btn">
        <el-date-picker
          v-model="year"
          type="year"
          size="small"
          placeholder="历年"
          format="YYYY年"
          value-format="YYYY"
          :teleported="false"
        />
      </div>
      <!-- 地图点击时展示的DOM元素 -->
      <div v-show="false" class="MapDetail-wrap">
        <MapDetail
          id="detailTooltip"
          :title="mapDetailData.title"
          :list="mapDetailData.list"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="CenterMap">
import { ref, onMounted, onBeforeUnmount, nextTick, watch, markRaw } from "vue";
import * as echarts from "echarts";
// import type { EChartsType, EChartsOption } from 'echarts';
// 标点详情组件
import MapDetail from "@/components/MapDetail.vue";
// echarts配置项图片资源
import buleSymbol from "@/assets/images/scatterWhitePosition.png";
// tools
import { emitter } from "@/utils/mitt";
import { debounce, reduce } from "lodash";

// 使用本地静态引入的地图数据
import hzMap from "@/assets/mapJson/3300.json";

// pinia
import { storeToRefs } from "pinia";
import { useDashBoardStore } from "@/stores";
import { usePersonalDoseStore } from "@/stores/personalDose";
const dashBoardStore = useDashBoardStore();
const personalStore = usePersonalDoseStore();

const { personSummaryCounts } = storeToRefs(personalStore); // 区域统计数据
// 获取地图上每个区域的统计数据 并更新option （不进行setOption渲染）
const getAreaStatistics = () => {
  const employeeCount = personSummaryCounts.value.map(
    (item) => item.employeeCount
  );
  const max = Math.max(...employeeCount);
  const declaredEmployerTotal = reduce(
    personSummaryCounts.value,
    (sum: number, item: mapAreaData) => sum + item.employerCount,
    0
  );
  const checkedEmployerTotal = reduce(
    personSummaryCounts.value,
    (sum: number, item: mapAreaData) => sum + item.institutionCount,
    0
  );
  const examCardTotal = reduce(
    personSummaryCounts.value,
    (sum: number, item: mapAreaData) => sum + item.employeeCount,
    0
  );
  // 忽略类型检查
  // @ts-ignore
  option.series[0].data = personSummaryCounts.value.map(
    (item: mapAreaData): mapAreaDetail => {
      return {
        ...item,
        value: Math.sqrt((item.employeeCount / max) * 10000), // 开根，看起来更平均
        list: [
          {
            name: "受检单位数",
            value: item.employerCount,
            total: declaredEmployerTotal,
          },
          {
            name: "检测机构数",
            value: item.institutionCount,
            total: checkedEmployerTotal,
          },
          {
            name: "放射工作人员数",
            value: item.employeeCount,
            total: examCardTotal,
          },
        ],
      };
    }
  );
  option.tooltip.show = true;
};

// 地图数据
let map = hzMap;
// 地图的名称 用于echarts注册mapJson
const mapName = ref("浙江省");

// 年份选择器
// const { year, code } = storeToRefs(dashBoardStore)
const disabledDate = (time: Date) => {
  const currentYear = new Date().getFullYear();
  return time.getFullYear() > currentYear;
};
const currentYear = new Date().getFullYear(); // 默认当前年份
const year = ref(currentYear + ""); // 转成字符串
const code = ref("");
// 选择年度变化时获取对应年份数据
watch(
  year,
  async (newVal) => {
    // 个人剂量
    personalStore.getDose_summaryemployees_great({
      year: newVal,
      code: code.value,
    });
    // 机构数据
    personalStore.institutions({
      year: newVal,
      code: code.value,
    });
    await personalStore.getPersonSummaryCountsData({
      year: newVal,
      code: code.value,
    }); // 在地图中调用触发

    await personalStore.getexchange_ratioData({
      year: newVal,
      code: code.value,
    }); //省级监督系统
    getAreaStatistics();
    registMap(mapName.value);
    draw();
  },
  { immediate: true } // 需要getAreaStatistics()，所以需要立即执行
);
// 点击切换区域时获取对应区域数据
watch(code, async (newVal: string | number) => {
  // newVal : 市4位string | 浙江省''
  // 个人剂量
  personalStore.getDose_summaryemployees_great({
    year: year.value,
    code: newVal,
  });
  //  按照职业类别聚合放射人员
  personalStore.personemployee_occupation({ code: newVal });
  // 机构数据
  personalStore.institutions({
    year: year.value,
    code: newVal,
  });
  await personalStore.getPersonSummaryCountsData({
    year: year.value,
    code: newVal,
  });
  // 在地图中调用触发
  getAreaStatistics();
  let ApiCode = newVal ? newVal : "3300";
  await getMapJson(ApiCode);
  registMap(mapName.value);
  draw();
});

// tooltip展示数据
const mapDetailData = ref<{ list: mapAreaDetailList; title: string }>({
  list: [],
  title: "",
});

// 获取mapJson
const getMapJson = async (code = "3300") => {
  // () => import(`@/assets/mapJson/${code}.json`)// 本地mapJson
  // const res = await fetch(`https://geo.datav.aliyun.com/areas_v2/bound/${code}_full.json`) // dataV API获取地图数据
  // const mapData = await res.json();
  // map = mapData;
  // return mapData;
  try {
    const module = await import(`../../assets/mapJson/${code}.json`);
    map = module.default;
    return map;
  } catch (error) {
    console.error(`Failed to load map JSON for code ${code}:`, error);
    return null;
  }
};

// 重新注册地图数据，并修改option
const registMap = (curMapName: string) => {
  // 注册新的地图数据
  echarts.registerMap(curMapName, JSON.parse(JSON.stringify(map)));
  option.series[0].map = curMapName;
  mapOptions.geo[0].map = curMapName;
};

onMounted(() => {
  initMap();
  emitter.on("resize", resizeChartfun);
});

// 地图ref
const mapRef = ref();
// echarts地图实例
const chartInstance = ref();
// 地图初始化 DOM节点挂载echarts实例、注册地图数据、阴影同步缩放拖拽
const initMap = () => {
  if (!mapRef.value) return;
  // 校验 Dom 节点上是否已经挂载了 ECharts 实例，只有未挂载时才初始化
  chartInstance.value = echarts.getInstanceByDom(mapRef.value);
  if (!chartInstance.value) {
    chartInstance.value = markRaw(
      echarts.init(mapRef.value, undefined, { renderer: "svg" })
    );
    //捕捉georoam事件，使下层的geo随着series一起缩放拖拽
    chartInstance.value.off("georoam"); // 防止重复绑定
    chartInstance.value.on(
      "georoam",
      function (params: { zoom: null | undefined }) {
        //获取缩放或拖拽后的option对象
        let roamOption = chartInstance.value.getOption();
        if (params.zoom != null && params.zoom != undefined) {
          //缩放
          roamOption.geo[0].zoom = roamOption.series[0].zoom; //下层geo的缩放等级跟着上层的geo一起改变
          roamOption.geo[0].center = roamOption.series[0].center; //下层的geo的中心位置随着上层geo一起改变
        } else {
          //拖拽
          roamOption.geo[0].center = roamOption.series[0].center;
        }
        chartInstance.value.setOption(roamOption);
      }
    );
    // 点击事件
    chartInstance.value.off("click"); // 防止重复绑定
    chartInstance.value.on(
      "click",
      (params: { data: { name: string; areaCode: string } }) => {
        const newName = params.data.name;
        const newCode = params.data.areaCode;

        if (newCode.length === 4) {
          // 4位 省级
          code.value = newCode;
          mapName.value = newName;
        } else {
          // 6位 市级
          code.value = "";
          mapName.value = "浙江省";
        }
      }
    );

    // 注册地图数据
    registMap(mapName.value);
    draw();
  }
};
// 地图绘制
const draw = () => {
  if (chartInstance.value) {
    chartInstance.value.setOption(option);
  }
};

// resize
const resizeChartfun = debounce(() => {
  nextTick(() => {
    chartInstance.value && chartInstance.value.resize();
    draw();
  });
}, 500);

// 地理坐标系组件 设置阴影
const mapOptions = {
  geo: [
    {
      name: "shadow",
      map: mapName.value,
      roam: false,
      zlevel: 4,
      label: {
        show: false,
      },
      itemStyle: {
        areaColor: "#0b3942",
        // 阴影颜色
        shadowColor: "#0b3942",
        // 偏移量
        shadowOffsetX: 3,
        shadowOffsetY: 26,
        borderWidth: 0,
      },
      animationDurationUpdate: 0,
      silent: true,
    },
  ],
};
// echarts配置项
const option = {
  ...mapOptions,
  series: [
    {
      // 地图
      name: "地图",
      type: "map",
      map: mapName.value,
      roam: true,
      itemStyle: {
        borderColor: "#17E3E3",
        borderWidth: 2,
        areaColor: "#003139",
      },
      zlevel: 5,
      showLegendSymbol: false,
      // 高亮样式
      emphasis: {
        itemStyle: {
          areaColor: "#ffd33c",
        },
        label: {
          color: "#fff",
        },
      },
      select: {
        disabled: true,
      },
      // 标签
      label: {
        show: true,
        color: "#fff",
        formatter: function (param: { name: string }) {
          var res = "{bg|" + param.name + "} " + "\n" + "{arrow|}";
          return res;
        },
        rich: {
          bg: {
            // width: 50,
            height: 18,
            fontSize: 12,
          },
          arrow: {
            width: 20,
            height: 20,
            backgroundColor: {
              image: buleSymbol,
            },
          },
        },
      },
      data: [],
      animationDurationUpdate: 0,
    },
  ],
  // 视觉映射（map地图按data.value数据值显示颜色）
  visualMap: {
    show: false,
    // left: 'right',
    // text: ['High', 'Low'], // 文本，默认为数值文本
    min: 0,
    max: 100,
    inRange: {
      color: [
        "#003139", // min
        "#0eb2d3", // max
      ],
    },
    calculable: true,
  },
  // 悬浮提示
  tooltip: {
    show: false,
    trigger: "item",
    triggerOn: "mousemove",
    showDelay: 0,
    // transitionDuration: 0.2,
    // 默认边框和背景
    padding: 0,
    borderWidth: 0,
    borderColor: "transparent",
    backgroundColor: "transparent",
    textStyle: {
      color: "#FFFFFF",
      fontSize: 12,
    },
    extraCssText: "box-shadow: none;",
    // 自定义
    formatter: function (params: {
      name: string;
      data: { list: { name: string; value: number; total: number }[] };
    }) {
      // 更新数据
      mapDetailData.value.title = params.name;
      mapDetailData.value.list = [];
      if (params.data && params.data.list && params.data.list.length) {
        const list = params.data.list.map(
          (item: { name: string; value: number; total: number }) => {
            let percentage = 0;
            if (item.total) {
              percentage = +((item.value / item.total) * 100).toFixed(2);
            }
            return {
              name: item.name,
              value: item.value,
              total: item.total,
              percentage,
            };
          }
        );
        mapDetailData.value.list = list;
      }
      return document.getElementById("detailTooltip");
      // }
    },
  },
};

onBeforeUnmount(() => {
  emitter.off("resize", resizeChartfun);
  // 手动销毁echarts实例
  if (chartInstance.value) {
    chartInstance.value.dispose();
  }
});
</script>

<style scoped lang="scss">
.centerMap {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .centerMap-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .map {
    width: vw(800);
    // height: vh(525);
    height: 100%;
  }

  .MapDetail-wrap {
    position: absolute;
  }
}

.yearSelect-btn {
  position: absolute;
  top: 3vh;
  left: 6vw;

  :deep(.el-input) {
    font-size: vw(18);
  }

  :deep(.el-date-editor) {
    --el-date-editor-width: calc(4.44vw * 1.4);
    --el-input-height: calc(1.2vw * 1.4);
    // --el-date-editor-width: vh(105 * 1.2);
    // --el-input-height: vh(28 * 1.2);
  }

  :deep(.el-input__wrapper) {
    box-shadow: none;
    background-color: transparent;
    padding: 0 vw(25);

    background-image: url(@/assets/images/yearSelect.svg);
    background-repeat: no-repeat;
    background-size: auto 100%;

    cursor: pointer;
  }

  :deep(.el-input__inner) {
    width: calc(3.2vw);
    height: 1.2vw;
    line-height: 1.2vw;
    -webkit-appearance: none;
    background-color: transparent;

    border: none;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 1em !important;
    font-weight: Medium;
    text-align: left;
    color: #18caca;
    display: inline-block;
    font-size: inherit;
    outline: 0;
    padding: 0;
    -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
    transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

    cursor: pointer;

    &::placeholder {
      color: #18caca;
    }
  }

  :deep(.el-select .el-input.is-focus .el-input__wrapper) {
    box-shadow: none;
  }

  :deep(.el-input__prefix) {
    display: none;
  }

  :deep(.el-input__suffix-inner > :first-child) {
    margin-left: 0px;
  }
}
</style>
