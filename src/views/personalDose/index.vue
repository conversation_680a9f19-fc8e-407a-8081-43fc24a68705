<template>
  <div class="dashboard-container">
    <Aside
      class="index-left"
      :topTitle="'年集体有效剂量地区分布'"
      :centerTitle="'人均年有效剂量地区分布'"
      :bottomTitle="'监测结果超1.25mSv人次数年度变化趋势图'"
    >
      <template #top>
        <Bar3D
          title="年集体有效剂量"
          :axisData="AnnualCollective.axisData"
          :seriesData="AnnualCollective.seriesData"
          :yAxisLabelShrink="false"
          yAxisName="人·Sv"
        />
      </template>
      <template #center>
        <ProgressList :seriesData="MeanCollective.seriesData"></ProgressList>
      </template>
      <template #bottom>
        <Line
          title="监测结果超1.25mSv人次数"
          :axisData="PersonalEmployer.axisData"
          :seriesData="PersonalEmployer.seriesData"
        ></Line>
      </template>
    </Aside>
    <Center class="index-center">
      <template #top>
        <TotalNum
          v-for="(item, i) in total"
          :key="i"
          :title="item.name"
          :total="item.value"
        ></TotalNum>
      </template>
      <template #map>
        <CenterMap></CenterMap>
      </template>
      <template #mapContainer>
        <div class="rate">
          <Rate
            title="省级监督系统"
            :rateValue1="RatioCollective.exchangeRatio"
            :rateValue2="RatioCollective.successRatio"
          ></Rate>
          <Rate right title="国家平台"></Rate>
        </div>
      </template>
      <template #bottom>
        <StatisticCards :seriesData="personalInstitutions.seriesData" />
      </template>
    </Center>
    <Aside
      class="index-right"
      :topTitle="'受检单位数地区分布'"
      :centerTitle="'监测中人员数地区分布'"
      :bottomTitle="'职业类别人数统计数据'"
    >
      <template #top>
        <Bar
          title="受检单位数"
          :axisData="personalExamCard.axisData"
          :seriesData="personalExamCard.seriesData"
          :colorIndex="1"
          yAxisName="家"
        />
      </template>
      <template #center>
        <Pie1
          title="监测中人员数"
          :seriesData="personalCheckedEmployer.seriesData"
          centerText="监测中人员数"
        />
      </template>
      <template #bottom>
        <List
          :seriesData="categoryResultcompu.listData"
          leftTitle="职业类别"
          leftWidth="67%"
          centerTitle=""
          centerWidth="0%"
          rightftTitle="人数"
          rightWidth="33%"
        />
      </template>
    </Aside>
  </div>
</template>

<script setup lang="ts" name="dashBoard">
import { ref, watch } from "vue";
// 布局组件
import Center from "@/components/layout/Center.vue";
import Aside from "@/components/layout/Aside.vue";
// 组件
import CenterMap from "./CenterMap.vue";
import TotalNum from "@/components/TotalNum.vue";
// 图表组件
import StatisticCards from "@/components/charts/StatisticCards.vue";
import PictorialBarCircle from "@/components/charts/PictorialBarCircle.vue";
import Pie1 from "@/components/charts/Pie1.vue";
import List from "@/components/charts/List.vue";
import Bar from "@/components/charts/Bar.vue";
import ProgressList from "@/components/charts/ProgressList.vue";
import Line from "@/components/charts/Line.vue";
import Rate from "./Rate.vue";
import Bar3D from "@/components/charts/Bar3D.vue";
// pinia
import { storeToRefs } from "pinia";
// pinia
import { usePersonalDoseStore } from "@/stores/personalDose";
const personalDoseStore = usePersonalDoseStore();
personalDoseStore.getDose_summaryemployees_great(); //获取左下监测结果超1.25mSv人次数 数据
personalDoseStore.getPersonSummaryCountsData(); // 受检单位数地区分布,检测中人员数地区分布
personalDoseStore.personemployee_occupation(); //  按照职业类别聚合放射人员
personalDoseStore.institutions(); //机构统计
personalDoseStore.getexchange_ratioData()//省级监督系统

const {
  personalExamCard,
  personalCheckedEmployer,
  AnnualCollective,
  MeanCollective,
  personalInstitutions,
  categoryResultcompu,
  RatioCollective,
} = storeToRefs(personalDoseStore);

const { PersonalEmployer, testingResult, categoryResult } =
  storeToRefs(personalDoseStore);
// 部分数据获取操作在CenterMap中触发，因为只有在CenterMap中有年份和地区选择的操作

// 地图上方地区总数
const total = ref([
  { name: "受检单位数", value: 0 },
  { name: "放射工作人员监测总人数", value: 0 },
]);
const { persondEmployerTotal, persondEmployeeTotal } =
  storeToRefs(personalDoseStore);
watch(
  [persondEmployerTotal, persondEmployeeTotal],
  (newVal) => {
    total.value[0].value = newVal[0];
    total.value[1].value = newVal[1];
  },
  { immediate: true, deep: true }
);

// const listData = [
//   { region: "核燃料循环-铀矿开采", date: "23" },
//   { region: "核燃料循环-铀矿加工", date: "48" },
//   { region: "核燃料循环-铀富集和转化", date: "76" },
//   { region: "医学应用-诊断放射学", date: "7" },
//   { region: "医学应用-核医学", date: "33" },
//   { region: "医学应用-放射治疗", date: "19" },
//   { region: "工业应用-工业辐照", date: "43" },
//   { region: "工业应用-工业探伤", date: "22" },
//   { region: "工业应用-发光涂料", date: "41" },
// ];

// const progressListData = [
//   { name: "杭州", value: 0},
//   { name: "宁波", value: 0},
//   { name: "温州", value: 0},
//   { name: "嘉兴", value: 0},
//   { name: "湖州", value: 0},
//   { name: "绍兴", value: 0},
//   { name: "金华", value: 0},
//   { name: "衢州", value: 0},
//   { name: "舟山", value: 0 },
//   { name: "台州", value: 0},
//   { name: "丽水", value: 0},
// ];
</script>

<style scoped lang="scss">
.dashboard-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;

  .index-left {
    width: 25%;
  }

  .index-center {
    width: 50%;
  }

  .rate {
    width: 100%;
    height: vh(90);
    position: absolute;
    bottom: vh(25);
    padding: 0 10%;

    color: #fff;

    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .index-right {
    width: 25%;

    .yearSelect-btn {
      position: absolute;
      top: calc(-1.2vw * 1.33 - 4px);
      left: calc(4.44vw * 1.33 * 2);

      :deep(.el-input) {
        font-size: vw(18);
      }

      :deep(.el-date-editor) {
        --el-date-editor-width: calc(4.44vw * 1.33);
        --el-input-height: calc(1.2vw * 1.33);
        // --el-date-editor-width: vh(105 * 1.2);
        // --el-input-height: vh(28 * 1.2);
      }

      :deep(.el-input__wrapper) {
        box-shadow: none;
        background-color: transparent;
        padding: 0 vw(25);

        background-image: url(@/assets/images/yearSelect.svg);
        background-repeat: no-repeat;
        background-size: auto 100%;

        cursor: pointer;
      }

      :deep(.el-input__inner) {
        width: calc(3.2vw);
        height: 1.2vw;
        line-height: 1.2vw;
        -webkit-appearance: none;
        background-color: transparent;

        border: none;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        font-size: 1em !important;
        font-weight: Medium;
        text-align: left;
        color: #18caca;
        display: inline-block;
        font-size: inherit;
        outline: 0;
        padding: 0;
        -webkit-transition: border-color 0.2s
          cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        cursor: pointer;

        &::placeholder {
          color: #18caca;
        }
      }

      :deep(.el-select .el-input.is-focus .el-input__wrapper) {
        box-shadow: none;
      }

      :deep(.el-input__prefix) {
        display: none;
      }

      :deep(.el-input__suffix-inner > :first-child) {
        margin-left: 0px;
      }
    }
  }
}
</style>
