<template>
  <div class="dashboard-container">
    <Aside class="index-left" :topTitle="'申报企业数地区分布'" :centerTitle="'申报实时数据'" :bottomTitle="'检测企业数地区分布'">
      <template #top>
        <Bar3D :title="'申报企业数'" :axisData="DeclaredEmployer.axisData" :seriesData="DeclaredEmployer.seriesData" />
      </template>
      <template #center>
        <List :seriesData="latestDeclare" />
      </template>
      <template #bottom>
        <Pie1 :title="'检测企业数'" :seriesData="CheckedEmployer.seriesData" />
      </template>
    </Aside>
    <Center class="index-center">
      <template #top>
        <TotalNum v-for="(item, i) in total" :key="i" :title="item.name" :total="item.value"></TotalNum>
      </template>
      <template #map>
        <CenterMap></CenterMap>
      </template>
      <template #bottom>
        <StatisticCards :seriesData="[...orgInstitution]" />
      </template>
    </Center>
    <Aside class="index-right" :topTitle="'体检个案地区分布'" :centerTitle="'新发职业病例地区分布'" :bottomTitle="'尘肺病康复站地区分布'">
      <template #top>
        <Bar :title="'体检个案数'" :axisData="ExamCard.axisData" :seriesData="ExamCard.seriesData" :colorIndex="1" />
      </template>
      <template #center>
        <Pie2 :title="'新发职业病例'" :seriesData="NewOccupationalDiseases" />
        <!-- <div style="width: 100%;height: 100%;position: relative;">
          <div class="yearSelect-btn">
            <el-date-picker v-model="OccYear" type="year" size="small" :clearable="false" format="YYYY年"
              value-format="YYYY" :teleported="false" @change="OccYearChange" />
          </div>
          <Pie2 :seriesData="NewOccupationalDiseases" />
        </div> -->
      </template>
      <template #bottom>
        <PictorialBarTriangle :title="'尘肺病康复站数'" :axisData="RehabilitationStation.axisData"
          :seriesData="RehabilitationStation.seriesData" />
      </template>
    </Aside>
  </div>
</template>

<script setup lang="ts" name="dashBoard">
import { ref, watch } from "vue";
/* 布局、具体组件、数据分开 ，index中直接对整体进行布局，插入需要的组件，组件中只负责展示数据 */
// 布局组件
import Center from "@/components/layout/Center.vue";
import Aside from "@/components/layout/Aside.vue";
// 组件
import CenterMap from "./CenterMap.vue";
import TotalNum from "@/components/TotalNum.vue";
// 图表组件
import Bar from "@/components/charts/Bar.vue";
import PictorialBarCircle from "@/components/charts/PictorialBarCircle.vue";
import PictorialBarTriangle from "@/components/charts/PictorialBarTriangle.vue";
import Pie1 from "@/components/charts/Pie1.vue";
import Pie2 from "@/components/charts/Pie2.vue";
import List from "@/components/charts/List.vue";
import StatisticCards from "@/components/charts/StatisticCards.vue";
import Bar3D from "@/components/charts/Bar3D.vue";

// pinia
import { storeToRefs } from 'pinia';
import { useHomeStore } from '@/stores/home';
// const dashBoardStore = useDashBoardStore();
const homeStore = useHomeStore();

// 新发职业病年份选择
// const OccYear = ref(new Date().getFullYear() + '');
// const OccYearChange = (val: string) => {
//   homeStore.getNewOccupationalDiseasesData({ year: val });
// }

// 初始数据获取
homeStore.getOrgInstitutionData(); // 机构统计
homeStore.getNewOccupationalDiseasesData({ year: new Date().getFullYear() + '' });// 新发职业病
homeStore.getRehabilitationStationData(); // 尘肺病康复站
homeStore.getDeclaredEmployer(); // 最新申报企业

// 部分数据获取操作在CenterMap中触发，因为只有在CenterMap中有年份和地区选择的操作

// 地图上方地区总数
const total = ref([
  { name: '企业申报数', value: 0 },
  { name: '体检个案数', value: 0 }
])
const { declaredEmployerTotal, examCardTotal } = storeToRefs(homeStore)
watch([declaredEmployerTotal, examCardTotal], (newVal) => {
  total.value[0].value = newVal[0];
  total.value[1].value = newVal[1];
}, { immediate: true, deep: true })

// 子区域统计数据
const { DeclaredEmployer, CheckedEmployer, ExamCard } = storeToRefs(homeStore)
// 新发职业病、尘肺病康复站、最新申报、机构统计
const { NewOccupationalDiseases, RehabilitationStation, latestDeclare, orgInstitution } = storeToRefs(homeStore)

</script>

<style scoped lang="scss">
.dashboard-container {
  width: 100%;
  height: 100%;
  display: flex;

  .index-left {
    width: 25%;
  }

  .index-center {
    width: 50%;
  }

  .index-right {
    width: 25%;

    .yearSelect-btn {

      position: absolute;
      top: calc(-1.2vw * 1.33 - 4px);
      left: calc(4.44vw * 1.33 *2);

      :deep(.el-input) {
        font-size: vw(18);
      }

      :deep(.el-date-editor) {
        --el-date-editor-width: calc(4.44vw * 1.33);
        --el-input-height: calc(1.2vw * 1.33);
        // --el-date-editor-width: vh(105 * 1.2);
        // --el-input-height: vh(28 * 1.2);
      }

      :deep(.el-input__wrapper) {
        box-shadow: none;
        background-color: transparent;
        padding: 0 vw(25);

        background-image: url(@/assets/images/yearSelect.svg);
        background-repeat: no-repeat;
        background-size: auto 100%;

        cursor: pointer;
      }

      :deep(.el-input__inner) {
        width: calc(3.2vw);
        height: 1.2vw;
        line-height: 1.2vw;
        -webkit-appearance: none;
        background-color: transparent;

        border: none;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        font-size: 1em !important;
        font-weight: Medium;
        text-align: left;
        color: #18caca;
        display: inline-block;
        font-size: inherit;
        outline: 0;
        padding: 0;
        -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        cursor: pointer;

        &::placeholder {
          color: #18caca;
        }
      }

      :deep(.el-select .el-input.is-focus .el-input__wrapper) {
        box-shadow: none;
      }

      :deep(.el-input__prefix) {
        display: none;
      }

      :deep(.el-input__suffix-inner > :first-child) {
        margin-left: 0px;
      }
    }
  }
}
</style>
