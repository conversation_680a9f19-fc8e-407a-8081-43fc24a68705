<template>
    <div class="OrgStatistic-outerContainer">
        <div class="statisticItem">
            <scatter :title="'职业卫生技术服务机构数'" :axis-data="orgCountArea.axisData" :series-data="orgCountArea.seriesData">
            </scatter>
        </div>
        <div class="statisticItem">
            <Radar :axis-data="empCountHazard.axisData" :series-data="empCountHazard.seriesData">
            </Radar>
        </div>
        <div class="statisticItem">
            <Pie3D :series-data="empCountSize"></Pie3D>
        </div>
    </div>
</template>


<script setup lang="ts" name="OrgStatistic">
import Pie3D from "@/components/charts/Pie3D.vue";
import Radar from "@/components/charts/Radar.vue";
import scatter from "@/components/charts/scatter.vue";

// pinia
import { storeToRefs } from 'pinia';
import { useDetectionStore } from '@/stores/detection'
const detectionStore = useDetectionStore()

const { orgCountArea, empCountHazard, empCountSize } = storeToRefs(detectionStore)

</script>

<style scoped lang="scss">
.OrgStatistic-outerContainer {
    width: 100%;
    height: 100%;
    display: flex;

    .statisticItem {
        width: calc(100% /3);
        height: 100%;
    }
}
</style>