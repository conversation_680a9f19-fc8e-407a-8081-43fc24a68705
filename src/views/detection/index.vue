<template>
  <div class="dashboard-container">
    <Aside class="index-left" :topTitle="'检测企业地区分布'" :centerTitle="'检测企业地区申报率'" :bottomTitle="'检测企业行业分布'">
      <template #top>
        <PictorialBarTriangle :title="'检测企业数'" :axisData="empCountArea.axisData"
          :seriesData="empCountArea.seriesData" />
      </template>
      <template #center>
        <Bar3D :axis-data="declareRateArea.axisData" :series-data="declareRateArea.seriesData"
          :tooltip="{ formatter: rateTooltipFormatter }" />
      </template>
      <template #bottom>
        <ProgressList :seriesData="industryEmpCount" />
      </template>
    </Aside>
    <Center class="index-center" :title="['职业卫生技术服务机构分布', '检测点危害因素分布', '检测企业规模分布']">
      <template #top>
        <TotalNum v-for="(item, i) in total" :key="i" :title="item.name" :total="item.value"></TotalNum>
      </template>
      <template #map>
        <CenterMap></CenterMap>
      </template>
      <template #bottom>
        <OrgStatistic />
      </template>
    </Center>
    <Aside class="index-right" :topTitle="'检测报告数历年趋势'" :centerTitle="'各行业检测点超标情况'" :bottomTitle="'各地区检测点超标情况'">
      <template #top>
        <Line showSymbol :title="'检测报告数'" :color-index="1" :showYAxisSplitLine="false"
          :axis-data="cardTrendYear.axisData" :series-data="cardTrendYear.seriesData" />
      </template>
      <template #center>
        <SingleBarWithLine :axis-data="pointExceedIndustryData.axisData" :bar="pointExceedIndustryData.barData"
          :line="pointExceedIndustryData.lineData" :tooltip="{ formatter: ExceedIndustryFormatter }"
          :series-name="['检测点数', '超标率']" />
      </template>
      <template #bottom>
        <SingleBarWithLine :axis-data="pointExceedAreaData.axisData" :bar="pointExceedAreaData.barData"
          :line="pointExceedAreaData.lineData" :tooltip="{ formatter: ExceedAreaFormatter }" showNumber
          :series-name="['检测点数', '超标率']" :barWidth="12" />
      </template>
    </Aside>
  </div>
</template>

<script setup lang="ts" name="dashBoard">
import { ref, watch, computed } from "vue";
/* 布局、具体组件、数据分开 ，index中直接对整体进行布局，插入需要的组件，组件中只负责展示数据 */
// 布局组件
import Center from "@/components/layout/Center.vue";
import Aside from "@/components/layout/Aside.vue";
// 组件
import CenterMap from "./CenterMap.vue";
import TotalNum from "@/components/TotalNum.vue";
import OrgStatistic from "./OrgStatistic.vue";
// 图表组件
import PictorialBarTriangle from "@/components/charts/PictorialBarTriangle.vue";
import Bar3D from "@/components/charts/Bar3D.vue";
import Line from "@/components/charts/Line.vue";
import ProgressList from "@/components/charts/ProgressList.vue";
import SingleBarWithLine from "@/components/charts/SingleBarWithLine.vue";
// pinia
import { storeToRefs } from 'pinia';
import { useDetectionStore } from '@/stores/detection'
const detectionStore = useDetectionStore()

const {
  total,
  empCountArea, declareRateArea, industryEmpCount,
  cardTrendYear, pointExceedIndustry, pointExceedIndustryData, pointExceedArea, pointExceedAreaData
} = storeToRefs(detectionStore)

watch(pointExceedIndustryData, (val) => {
  console.log('pointExceedIndustryData', val)
},
  { deep: true }
)

const rateTooltipFormatter = (params) => {
  const i = params[0].dataIndex
  return `
    <div style="
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
      justify-content: space-around;"
    >
      <p>${params[0].axisValue}</p>
      <p>申报数：<span style="color:#17E3E3;font-weight:bold;">${declareRateArea.value.data[i].declareEmpCount}</span></p>
      <p>检测企业数：<span style="color:#17E3E3;font-weight:bold;">${declareRateArea.value.data[i].detectionEmpCount}</span></p>
    </div>
    `
}

const ExceedIndustryFormatter = (params) => {
  const i = params[0].dataIndex
  return `
    <div style="
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
      justify-content: space-around;"
    >
      <p>${params[0].axisValue}</p>
      <p>报告数：<span style="color:#17E3E3;font-weight:bold;">${pointExceedIndustryData.value.barData[i]}</span></p>
      <p>超标数：<span style="color:#FFD33C;font-weight:bold;">${pointExceedIndustry.value[i].exceedPointCount}</span></p>
      <p>超标率：<span style="color:#FFD33C;font-weight:bold;">${pointExceedIndustryData.value.lineData[i]}</span></p>
    </div>
    `
}

const ExceedAreaFormatter = (params) => {
  const i = params[0].dataIndex
  return `
    <div style="
      display: flex;
      flex-direction: column;
      height: 100%;
      width: 100%;
      justify-content: space-around;"
    >
      <p>${params[0].axisValue}</p>
      <p>报告数：<span style="color:#17E3E3;font-weight:bold;">${pointExceedAreaData.value.barData[i]}</span></p>
      <p>超标数：<span style="color:#FFD33C;font-weight:bold;">${pointExceedArea.value[i].exceedPointCount}</span></p>
      <p>超标率：<span style="color:#FFD33C;font-weight:bold;">${pointExceedAreaData.value.lineData[i]}</span></p>
    </div>
    `
}

</script>

<style scoped lang="scss">
.dashboard-container {
  width: 100%;
  height: 100%;
  display: flex;

  .index-left {
    width: 25%;
  }

  .index-center {
    width: 50%;
  }

  .index-right {
    width: 25%;

    .yearSelect-btn {

      position: absolute;
      top: calc(-1.2vw * 1.33 - 4px);
      left: calc(4.44vw * 1.33 *2);

      :deep(.el-input) {
        font-size: vw(18);
      }

      :deep(.el-date-editor) {
        --el-date-editor-width: calc(4.44vw * 1.33);
        --el-input-height: calc(1.2vw * 1.33);
        // --el-date-editor-width: vh(105 * 1.2);
        // --el-input-height: vh(28 * 1.2);
      }

      :deep(.el-input__wrapper) {
        box-shadow: none;
        background-color: transparent;
        padding: 0 vw(25);

        background-image: url(@/assets/images/yearSelect.svg);
        background-repeat: no-repeat;
        background-size: auto 100%;

        cursor: pointer;
      }

      :deep(.el-input__inner) {
        width: calc(3.2vw);
        height: 1.2vw;
        line-height: 1.2vw;
        -webkit-appearance: none;
        background-color: transparent;

        border: none;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
        font-size: 1em !important;
        font-weight: Medium;
        text-align: left;
        color: #18caca;
        display: inline-block;
        font-size: inherit;
        outline: 0;
        padding: 0;
        -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

        cursor: pointer;

        &::placeholder {
          color: #18caca;
        }
      }

      :deep(.el-select .el-input.is-focus .el-input__wrapper) {
        box-shadow: none;
      }

      :deep(.el-input__prefix) {
        display: none;
      }

      :deep(.el-input__suffix-inner > :first-child) {
        margin-left: 0px;
      }
    }
  }
}
</style>
