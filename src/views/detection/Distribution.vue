<template>
    <div class="OrgStatistic-outerContainer">
        <div class="statisticItem " v-for="(item, i) in titleList" :key="i">
            <div class="gap"></div>
            <div class="ItemTitle">
                <img class="icon" :src="iconList[i]"></img>
                <div class="text">{{ item }}</div>
            </div>
            <div class="ItemContent">
                <ProgressBar></ProgressBar>
            </div>
        </div>

    </div>
</template>


<script setup lang="ts" name="OrgStatistic">
import ProgressBar from '@/components/charts/ProgressBar.vue'

import icon1 from '@/assets/images/矿山2.svg'
import icon2 from '@/assets/images/煤矿2.svg'
import icon3 from '@/assets/images/非煤矿2.svg'
import icon4 from '@/assets/images/冶金2.svg'
import icon5 from '@/assets/images/水泥2.svg'
import icon6 from '@/assets/images/陶瓷2.svg'
import icon7 from '@/assets/images/石材加工2.svg'

const titleList = ['矿山', '煤矿', '非煤矿', '冶金', '水泥', '陶瓷', '石材加工']
const iconList = [icon1, icon2, icon3, icon4, icon5, icon6, icon7]

</script>

<style scoped lang="scss">
.OrgStatistic-outerContainer {
    width: 100%;
    height: 100%;
    display: flex;
    // gap: vw(25);

    .statisticItem {
        width: calc(100% / 7 - 1px); // 不能直接使用 flex: 1; 会导致子元素大于父元素
        height: 100%;
        display: flex;
        flex-direction: column;

        .gap {
            width: 100%;
            height: vh(8);
        }

        .ItemTitle {
            height: vh(21);
            width: 100%;
            padding-left: 4%;

            background-image: url(@/assets/images/ProgressBarItem.png);
            background-size: 85% 100%;
            background-repeat: no-repeat;

            display: flex;
            align-items: center;

            .icon {
                height: 100%;
                // height: vh(21 * 2);
            }

            .text {
                font-size: vh(16);
                letter-spacing: 0.08px;
                font-family: YouSheBiaoTiHei;
                font-weight: normal;
                line-height: normal;
                color: linear-gradient(180deg, #FFFFFF 32%, #18CACA 87%);
                background: linear-gradient(180deg, #FFFFFF 32%, #18CACA 87%);
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
                text-fill-color: transparent;
            }


        }

        .ItemContent {
            width: 100%;
            height: calc(100% - vh(21 + 7));
        }
    }
}
</style>