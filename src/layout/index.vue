<template>
  <el-container class="layout-container" id="layout-container">
    <TopBar class="topBar" />
    <div class="main">
      <Main ref="layoutMainRef" />
    </div>
  </el-container>
</template>

<script setup lang="ts" name="Layout">
// import { authenticate } from '@/api/index';
// components
import TopBar from './TopBar.vue';
import Main from './Main.vue';
// utils
import { onMounted, onBeforeUnmount } from 'vue';
import { emitter } from "@/utils/mitt";
import { debounce } from "lodash";
// pinia
import { useDashBoardStore } from '@/stores';
// router
// import { useRoute } from 'vue-router';
// pinia
const dashBoardStore = useDashBoardStore();
// router
// const route = useRoute();

// 获取路由参数里的code，登录验证
// const queryParams = route.query;
// console.log('queryParams🍊', queryParams);
// let code = queryParams.code as string;
// authenticate({ code })

// Resize event 计算window宽高
const resizeHandler = () => {
  const offsetHeight = (document.getElementsByClassName('layout-container')[0] as HTMLElement).offsetHeight;
  const offsetWidth = (document.getElementsByClassName('layout-container')[0] as HTMLElement).offsetWidth;
  console.log('window🍊', offsetWidth, offsetHeight);

  dashBoardStore.setOffsetWH(offsetWidth, offsetHeight);
  emitter.emit("resize"); // 触发resize事件（触发时会同时触发子组件上的emitter.on("resize")）
}
const resizeFun = debounce(resizeHandler, 333);
onMounted(() => {
  resizeHandler();
  // 监听resize/fullscreenchange
  window.addEventListener("resize", resizeFun);
  window.addEventListener("fullscreenchange", resizeFun);
});
// 移除监听(好像没必要)
onBeforeUnmount(() => {
  emitter.off("resize");
});

</script>

<style lang="scss" scoped>
/* Component styles go here */
.layout-container {
  width: 100vw;
  height: 100vh;

  overflow: hidden;

  background: url("@/assets/images/bg.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  /* 当内容高度大于图片高度时，背景图像的位置相对于viewport固定 */
  background-attachment: fixed;

  display: flex;
  flex-direction: column;

  padding-bottom: vh(13.5);

  .topBar {
    flex-grow: 0;
    flex-shrink: 0;
    flex-basis: auto;
  }

  .main {
    // flex: 1;
    height: calc(100vh - vh(13.5) - vw(126));
  }

  // .layout-paddingBottom {
  //   flex-grow: 0;
  //   flex-shrink: 0;
  // }
}
</style>