<template>
  <div class="top-bar">
    <!-- 标题 -->
    <div class="content">
      <div class="btns">
        <div v-for="(item, i) in navList" :key="i" class="nav-btn" :class="{ active: item.path === activePath }"
          @click="navClick(item)">
          {{ item.name }}
        </div>
      </div>
      <div class="title">{{ activeTitle }}</div>
      <div class="btns">
        <div class="user">
          {{ userName }}
        </div>
        <el-button type="primary" size="default" class="exit-btn" @click="exit" />
      </div>
    </div>
    <div ref="lottieTopBar" class="lottieTopBar"></div>
  </div>
</template>
<script setup lang="ts" name="TopBar">
// utils
import { ref, onMounted } from "vue";
import { emitter } from "@/utils/mitt";
// lottie
import lottie from "lottie-web";
import animationData from "@/assets/lottie/topBar.json"; // 路径需要根据实际情况修改
// pinia
import { useDashBoardStore } from '@/stores';
// vue-router
import { useRouter } from "vue-router";
// pinia
const dashBoardStore = useDashBoardStore();
// vue-router
const router = useRouter();

const userName = ref('浙江省疾控中心');

const navList = [
  { name: "首页", path: "/home", title: '浙江省职业健康信息服务与监管平台' },
  { name: "申报", path: "/report", title: '浙江省职业健康信息服务与监管平台' },
  { name: "检测", path: "/detection", title: '浙江省职业健康信息服务与监管平台' },
  { name: "体检", path: "/exam", title: '浙江省职业健康信息服务与监管平台' },
  { name: "职业病", path: "/disease", title: '浙江省职业健康信息服务与监管平台' },
  { name: "个人剂量", path: "/personalDose", title: '浙江省疾病预防控制中心' },
  { name: "医疗放射", path: "/medicalRadiation", title: '浙江省疾病预防控制中心' }
];

const activePath = ref('/');
const activeTitle = ref('浙江省职业健康信息服务与监管平台');

activePath.value = router.currentRoute.value.path;
const activeNav = navList.find(item => item.path === activePath.value);
if (activeNav) {
  activeTitle.value = activeNav.title;
}

const navClick = (item: { name: string, path: string, title: string }) => {
  if (activePath.value !== item.path) {
    dashBoardStore.resetYearAndCode();
  }
  activePath.value = item.path;
  activeTitle.value = item.title;
  router.push(item.path);
  fullScreen();
  emitter.emit("resize");// 手动触发一下resize事件，确保子组件的resizeFun方法被调用
}

const lottieTopBar = ref();
const initLottie = () => {
  lottie.loadAnimation({
    container: lottieTopBar.value, // 在模板中添加一个 ref="lottieContainer" 的容器
    animationData: animationData,
    renderer: "svg", // 选择渲染器，可以是 'svg'、'canvas'、'html'
    loop: true, // 是否循环播放
    autoplay: true // 是否自动播放
  });
};

// 全屏
const launchFullscreen = (element: any) => {
  if (element.requestFullscreen) {
    element.requestFullscreen();
    dashBoardStore.setIsFullScreen(true);
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
    dashBoardStore.setIsFullScreen(true);
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen();
    dashBoardStore.setIsFullScreen(true);
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullScreen();
    dashBoardStore.setIsFullScreen(true);
  }
};

// 将layout-container全屏
const fullScreen = () => {
  console.log('launchFullscreen!!!');
  launchFullscreen(document.getElementById("layout-container"));
};

const isFullScreen = () => {
  return document.fullscreenElement !== null;
};

// eslint忽略此函数
const exit = () => {
  // fullScreen()
  if (isFullScreen()) {
    if (document.exitFullscreen) {
      document.exitFullscreen();
      // @ts-ignore
    } else if (document.mozCancelFullScreen) { // Firefox 
      // @ts-ignore
      document.mozCancelFullScreen();
      // @ts-ignore
    } else if (document.webkitExitFullscreen) { // Chrome, Safari and Opera
      // @ts-ignore
      document.webkitExitFullscreen();
      // @ts-ignore
    } else if (document.msExitFullscreen) { // IE/Edge
      // @ts-ignore
      document.msExitFullscreen();
    }
  }
  dashBoardStore.setIsFullScreen(false);
  dashBoardStore.logout();
}

onMounted(() => {
  initLottie();
});
</script>

<style scoped lang="scss">
.top-bar {
  display: relative;
  height: vw(126);
  width: 100vw;

  @media screen and (max-width: 3840px) {
    font-size: 32px;
  }

  @media screen and (max-width: 3440px) {
    font-size: 24px;
  }

  @media screen and (max-width: 2560px) {
    font-size: 18px;
  }

  @media screen and (max-width: 1960px) {
    font-size: 14px;
  }

  @media screen and (max-width: 1440px) {
    font-size: 9px;
  }

  @media screen and (max-width: 1024px) {
    font-size: 7px;
  }

  .content {
    position: relative;
    width: 100%;
    padding: 0 vw(20);

    font-family: 思源黑体;
    font-size: 38px;
    font-weight: 500;
    line-height: normal;
    text-align: center;
    letter-spacing: 2px;
    // background: linear-gradient(180deg, #56F4FE 32%, #A3FFCD 85%);
    // -webkit-background-clip: text;
    // -webkit-text-fill-color: transparent;
    // background-clip: text;
    // text-fill-color: transparent;

    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .title {
    position: absolute;
    width: 100%;
    top: vw(24);
    left: 50%;
    transform: translateX(-50%);

    font-size: vw(38);
    font-weight: 500;
    line-height: normal;
    text-align: center;
    letter-spacing: 2px;
    background: linear-gradient(180deg, #56F4FE 32%, #A3FFCD 85%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }

  .btns {
    display: flex;
    align-items: center;
    gap: vw(8);
  }

  .user {
    width: vw(198);
    height: vw(75);
    position: relative;
    top: vw(4.5);
    background-image: url("@/assets/images/top-bar-user-bg.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    display: flex;
    align-items: center;
    padding-left: vw(54);
    padding-bottom: vw(6);
    font-size: vw(16);
    font-weight: 500;
    letter-spacing: 0px;
    color: #18CACA;
  }

  .nav-btn {
    background-color: transparent;
    background-image: url("@/assets/images/top-bar-nav-btn.svg");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: none;

    width: vw(82*0.9);
    height: vw(35*0.9);
    padding-bottom: vh(2);

    font-size: vw(20*0.9);
    font-family: YouSheBiaoTiHei;
    font-weight: normal;
    line-height: normal;
    text-align: center;
    letter-spacing: 0.28px;
    font-variation-settings: "opsz" auto;
    opacity: 1;
    color: #56F4FE;
    text-shadow: 0px 0px 5.7px #0AD6FB;

    display: flex;
    align-items: center;
    justify-content: center;

    // 激活状态
    &.active {
      color: #fff;
    }

    cursor: pointer;
    z-index: 9; // 防止被覆盖

    &:hover {
      //放大动效
      transform: scale(1.1);
      transition: all 0.33s;
    }
  }

  .exit-btn {
    background-color: transparent;
    background-image: url("@/assets/images/top-bar-exit-btn-bg.svg");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: none;
    width: vw(51);
    height: vw(33);
    position: relative;

    &:hover {
      //放大动效
      transform: scale(1.1);
      transition: all 0.33s;
    }
  }

  .lottieTopBar {
    width: 100%;
    height: auto;
    pointer-events: none;
    position: absolute;
    top: 0;
  }
}
</style>
