<template>
  <div class="layout-parent">
    <router-view v-slot="{ Component }">
      <!-- <transition :name="'opacitys'" mode="out-in"> -->
      <!-- dashboard-container -->
      <component :is="Component" ></component>
      <!-- </transition> -->
    </router-view>
  </div>
</template>

<script setup lang="ts" name="MainLayout">
</script>

<style lang="scss" scoped>
.layout-parent {
  width: 100%;
  height: 100%;

  flex: 1;
}

.opacitys-enter-active,
.opacitys-leave-active {
  will-change: transform;
  transition: all 0.3s ease;
}

.opacitys-enter-from,
.opacitys-leave-to {
  opacity: 0;
}
</style>