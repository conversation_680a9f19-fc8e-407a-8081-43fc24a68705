import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';

import App from './App.vue'
import router from './router'

// 引入重置样式
import "./style/reset.scss";

// 创建 pinia 实例
const pinia = createPinia();
// 为 pinia 实例添加持久化插件
pinia.use(piniaPluginPersistedstate);

const app = createApp(App)

app.use(pinia)
app.use(router)

app.mount('#app')
