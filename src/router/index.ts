import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'
import Home from '@/views/home/<USER>'
import Exam from '@/views/exam/index.vue'
import Detection from '@/views/detection/index.vue'
import Report from '@/views/report/index.vue'
import Disease from '@/views/disease/index.vue'
import personalDose from '@/views/personalDose/index.vue'
import MedicalRadiation from '@/views/medicalRadiation/index.vue'

import notFound from '@/views/Error/404.vue'
import Uncertified from '@/views/Error/Uncertified.vue'

import { authenticate } from '@/api/index'
import { useDashBoardStore } from '@/stores/index'

const WHITE_LIST = ['/uncertified', '/404']

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_PATH),
  routes: [
    {
      path: '/',
      name: 'layout',
      component: Layout,
      redirect: '/home',
      children: [
        {
          path: '/home',
          name: 'Home',
          component: Home, // 首页
        },
        {
          path: '/exam',
          name: 'Exam',
          component: Exam, // 体检
        },
        {
          path: '/detection',
          name: 'Detection',
          component: Detection, // 检测
        },
        {
          path: '/report',
          name: 'Report',
          component: Report, // 检测
        },
        {
          path: '/disease',
          name: 'Disease',
          component: Disease, // 检测
        },
        {
          path: '/personalDose',
          name: 'PersonalDose',
          component: personalDose, // 个人剂量
        },
        {
          path: '/medicalRadiation',
          name: 'MedicalRadiation',
          component: MedicalRadiation, // 医用放射
        }
      ]
    },
    {
      path: '/404',
      name: 'notFound',
      component: notFound,
    },
    {
      path: '/uncertified',
      name: 'Uncertified',
      component: Uncertified,
      // component: () => import('@/views/Error/Uncertified.vue'),
    },
    {
      path: '/:path(.*)*',
      redirect: '/404',
    }
  ]
})

// 路由加载前
router.beforeEach(async (to, from, next) => {
  // const dashBoardStore = useDashBoardStore()
  // const query = to.query
  // if (query.code) { // 如果路由参数中携带code，则直接使用code去获取token
  //   const res = await authenticate({ code: query.code as string })
  //   dashBoardStore.setToken(res.data.token)
  //   // 放行，但是删除query中的code
  //   const newQuery = { ...to.query, code: undefined }
  //   if (to.path === '/uncertified') {
  //     next({ path: '/', query: newQuery })
  //   }
  //   else { next({ path: to.path, query: newQuery }) }
  // } else if (dashBoardStore.getToken) {// 如果有token，则直接跳转
  //   // 如果token过期，获取数据时接口状态码会返回401，呗axios拦截器拦截之后清空token并跳转到uncertified
  //   // console.log('有token', dashBoardStore.getToken)
  //   if (to.path === '/uncertified') {
  //     next('/')
  //   } else { next() }
  // } else {
  //   // 如果没有token，则跳转到uncertified
  //   // console.log('没有token')
  //   if (WHITE_LIST.includes(to.path)) { // 白名单;防止未登录状态无限重定向
  //     next()
  //   } else {
  //     next('/uncertified')
  //   }
  // }
  next()
});

// 路由加载后
// router.afterEach(() => {
// 
// });

export default router
