<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'

</script>

<template>
  <RouterView />
</template>

<style lang="scss">
.border1 {
  border: 1px dashed white;
  box-sizing: border-box;
}

.w100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

@font-face {
  font-family: 'FX-LED';
  src: url('@/assets/fonts/FX-LED.TTF') format('truetype');
}
@font-face {
  font-family: 'DS-digital';
  src: url('@/assets/fonts/DS-digital normal.TTF') format('truetype');
}
@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('@/assets/fonts/YouSheBiaoTiHei.ttf') format('truetype');
}
</style>
