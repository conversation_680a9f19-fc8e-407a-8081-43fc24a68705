import request from '@/utils/request';
/**
 *  检测企业地区分布 
 */
export function getCountGroupArea(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/empCountGroupByArea',
    method: 'get',
    params: query,
  });
}
/**
 *  检测企业地区申报率 
 */
export function getDeclareRateArea(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/empDeclareRateByArea',
    method: 'get',
    params: query,
  });
}
/**
 *  检测企业行业分布 
 */
export function getIndustryEmpCount(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/empCountGroupByIndustry',
    method: 'get',
    params: query,
  });
}
/**
 *  检测报告历年趋势 
 */
export function getCardTrendYear(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/reportCountTrendByYear',
    method: 'get',
    params: query,
  });
}
/**
 *  各行业检测点超标情况 
 */
export function getPointExceedIndustry(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/pointExceedCountByIndustry',
    method: 'get',
    params: query,
  });
}
/**
 *  各地区检测点超标情况 
 */
export function getPointExceedArea(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/pointExceedCountByArea',
    method: 'get',
    params: query,
  });
}
/**
 *  检测企业规模分布 
 */
export function getEmpCountSize(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/empCountGroupBySize',
    method: 'get',
    params: query,
  });
}
/**
 *  职业卫生技术服务机构分布 
 */
export function getOrgCountArea(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/orgCountByArea',
    method: 'get',
    params: query,
  });
}

/**
 *  企业检测危害因素分布
 */
export function getEmpCountHazard(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/empCountGroupByHazard',
    method: 'get',
    params: query,
  });
}
/**
 *  检测报告数地区分布
 */
export function getReportCountArea(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/reportCountByArea',
    method: 'get',
    params: query,
  });
}

// 旧设计
/**
 *  检测企业重点行业分布
 */
export function getImportantIndustry(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/empCountGroupByImportantIndustry',
    method: 'get',
    params: query,
  });
}

/**
 *  检测企业行业分布
 */
export function getIndustry(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/empCountGroupByIndustry',
    method: 'get',
    params: query,
  });
}
/**
 *  企业危害因素超标分布
 */
export function getExceedHazard(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/empCountGroupByExceedHazard',
    method: 'get',
    params: query,
  });
}

