import request from '@/utils/request';

/**
 *  体检个案数地区分布
 */
export function getAreaCardCountGroup(query?: { year?: string | null; }) {
  return request({
    url: '/exam/cardCountGroupByArea',
    method: 'get',
    params: query,
  });
}

/**
 *  报告率
 */
export function getReportRate(query?: { year?: string | null; }) {
  return request({
    url: '/exam/reportRateGroupByArea',
    method: 'get',
    params: query,
  });
}

/**
 *  体检企业行业分布
 */
export function getIndustryEmpCount(query?: { year?: string | null; }) {
  return request({
    url: '/exam/empCountGroupByIndustry',
    method: 'get',
    params: query,
  });
}

/**
 *  体检企业规模分布
 */
export function getIndustryEnterpriseSize(query?: { year?: string | null; }) {
  return request({
    url: '/exam/empGroupByEnterpriseSize',
    method: 'get',
    params: query,
  });
}

/**
 *  体检危害因素分布
 */
export function getHazardCardCount(query?: { year?: string | null; }) {
  return request({
    url: '/exam/cardCountGroupByHazard',
    method: 'get',
    params: query,
  });
}

/**
 *  个案卡历年趋势
 */
export function getCardCountYearTrend(query?: { year?: string | null; }) {
  return request({
    url: '/exam/cardCountTrendByYear',
    method: 'get',
    params: query,
  });
}

/**
 *  个案卡行业疑似和禁忌证检出情况
 */
export function getIndustryCardDiagnosis(query?: { year?: string | null; }) {
  return request({
    url: '/exam/cardDiagnosisCountByIndustry',
    method: 'get',
    params: query,
  });
}
/**
 *  个案卡地区疑似禁忌检出情况
 */
export function getAreaCardDiagnosis(query?: { year?: string | null; }) {
  return request({
    url: '/exam/cardDiagnosisCountByArea',
    method: 'get',
    params: query,
  });
}
/**
 *  职业健康检查机构分布
 */
export function getExamOrgArea(query?: { year?: string | null; }) {
  return request({
    url: '/exam/examOrgCountByArea',
    method: 'get',
    params: query,
  });
}
/**
 *  体检企业地区分布
 */
export function getEmpGroupArea(query?: { year?: string | null; }) {
  return request({
    url: '/exam/empGroupByArea',
    method: 'get',
    params: query,
  });
}
