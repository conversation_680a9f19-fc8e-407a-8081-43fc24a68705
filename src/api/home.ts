import request from '@/utils/request';

/**
 *  获取区域申报企业，检测企业，个案卡数量
 */
export function getSummaryCounts(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/home/<USER>',
    method: 'get',
    params: query,
  });
}

/**
 *  获取最新申报企业数据
 */
export function getDeclaredEmployer(query?: { year?: string| null; code?: string }) {
  return request({
    url: '/home/<USER>',
    method: 'get',
    params: query,
  });
}

/**
 *  获取检测机构数据统计
 */
export function getInstitution(query?: { year?: string| null; code?: string }) {
  return request({
    url: '/home/<USER>',
    method: 'get',
    params: query,
  });
}

/**
 *  新增职业病统计
 */
export function getNewOccupationalDiseases(query: { year?: string| null; code?: string }) {
  return request({
    url: '/home/<USER>',
    method: 'get',
    params: query,
  });
}

/**
 *  尘肺病康复站统计
 */
export function getRehabilitationStation(query?: { code?: string }) {
  return request({
    url: '/home/<USER>',
    method: 'get',
    params: query,
  });
}


/**
 *  申报企业数和申报数地区分布
 */
export function getDeclareCountGroupArea(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/declare/countGroupByArea',
    method: 'get',
    params: query,
  });
}
/**
 *  检测企业地区分布 
 */
export function getDetectionCountGroupArea(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/detection/empCountGroupByArea',
    method: 'get',
    params: query,
  });
}
/**
 *  体检个案数地区分布
 */
export function getAreaCardCountGroup(query?: { year?: string | null; }) {
  return request({
    url: '/exam/cardCountGroupByArea',
    method: 'get',
    params: query,
  });
}