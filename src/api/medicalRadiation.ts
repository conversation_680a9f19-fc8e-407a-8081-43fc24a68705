import request from '@/utils/request';

/**
 *  获取机构总数，按照地区分组 map
 */
export function getInstitutionRegion(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/radiation/institution_region',
    method: 'get',
    params: query,
  });
}
/**
 *  获取设备数量，按照地区分组 map
 */
export function getDeviceRegion(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/radiation/device_region',
    method: 'get',
    params: query,
  });
}
/**
 *  获取报送单位数量，按照地区分组 左上
 */
export function getReportedCustomerOrgRegion(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/radiation/reported_customer_org_region',
    method: 'get',
    params: query,
  });
}
/**
 *  设备类型分组 左中
 */
export function getRadiationDeviceCategory(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/radiation/device_category',
    method: 'get',
    params: query,
  });
}
/**
 *  获取受检单位总数，按照地区分组 左下
 */
export function getRadiationEmployerRegion(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/radiation/employer_region',
    method: 'get',
    params: query,
  });
}
/**
 *  项目监测类型分组 右上
 */
export function getRadiationProjectType(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/radiation/project_type',
    method: 'get',
    params: query,
  });
}
/**
 *  获取不合格设备数量，按照地区分组 右中
 */
export function getUnqualifiedDeviceRegion(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/radiation/unqualified_device_region',
    method: 'get',
    params: query,
  });
}
/**
 *  单位类别分组 右下
 */
export function getRadiationEmployerType(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/radiation/employer_type',
    method: 'get',
    params: query,
  });
}

/**
 *  获取机构相关统计数据 中下
 */
export function getRadiationInstitutions(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/radiation/institutions',
    method: 'get',
    params: query,
  });
}
/**
 *  获取报告数量 中下
 */
export function getRadiationReports(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/radiation/reports',
    method: 'get',
    params: query,
  });
}


