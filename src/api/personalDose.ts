import request from "@/utils/request";

/**
 * 地图数据， 受检单位，检测中人员数地区分布
 */
export function getPersonSummaryCounts(query?: {
  year?: string | null;
  code?: string;
}) {
  return request({
    url: "/individualDose/summaryCounts",
    method: "get",
    params: query,
  });
}
/**
 *  监测结果超1.25mSv人次数
 */
export function getDose_summaryemployees_great(query?: {
  year?: string | null;
  code?: string;
}) {
  return request({
    url: "/individualDose/employees_great",
    method: "get",
    params: query,
  });
}
/**
 *  按照职业类别聚合放射人员
 */
export function getPersonemployee_occupation(query?: { code?: string }) {
  return request({
    url: "/individualDose/employee_occupation",
    method: "get",
    params: query,
  });
}

/**
 * 机构统计
 */
export function getInstitutions(query?: {
  year?: string | null;
  code?: string;
}) {
  return request({
    url: "/individualDose/institutions",
    method: "get",
    params: query,
  });
}

// 省平台的交换率和成功率
export function getexchange_ratio(query?: {
  year?: string | null;
  code?: string | null;
}) {
  return request({
    url: "/individualDose/exchange_ratio",
    method: "get",
    params: query,
  });
}
