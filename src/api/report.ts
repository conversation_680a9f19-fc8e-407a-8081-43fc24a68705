import request from '@/utils/request';

/**
 *  申报企业数和申报数地区分布
 */
export function getCountGroupArea(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/declare/countGroupByArea',
    method: 'get',
    params: query,
  });
}

/**
 *  申报企业重点行业分布
 */
export function getEmpImportantIndustry(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/declare/empCountGroupByImportantIndustry',
    method: 'get',
    params: query,
  });
}

/**
 *  申报企业行业分布
 */
export function getEmpIndustry(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/declare/empCountGroupByIndustry',
    method: 'get',
    params: query,
  });
}

/**
 *  申报企业经济类型分布
 */
export function getEmpEconomic(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/declare/empCountGroupByEconomic',
    method: 'get',
    params: query,
  });
}

/**
 *  申报企业规模分布
 */
export function getEmpEnterpriseSize(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/declare/empCountGroupByEnterpriseSize',
    method: 'get',
    params: query,
  });
}
/**
 *  申报企业情况分布
 */
export function getDeclareSituation(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/declare/declareSituation',
    method: 'get',
    params: query,
  });
}
/**
 *  每月申报趋势
 */
export function getDeclareTrend(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/declare/declareTrendGroupByMonth',
    method: 'get',
    params: query,
  });
}
/**
 *  初次申报地区分布
 */
export function getFirstDeclareTrend(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/declare/firstDeclareCountByArea',
    method: 'get',
    params: query,
  });
}
/**
 *  申报企业重点因素分布(申报企业情况分布)
 */
export function getEmpCountHazard(query?: { year?: string | null; code?: string }) {
  return request({
    url: '/declare/empCountByImportHazard',
    method: 'get',
    params: query,
  });
}
