{"name": "sjk_dashboard_front", "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:pro": "vite build --mode production", "build:custom": "node scripts/build-with-basepath.js", "build:custom:dev": "node scripts/build-with-basepath.js", "check-build": "run-p type-check build", "type-check": "vue-tsc --noEmit", "preview": "vite preview --port 4173"}, "dependencies": {"axios": "^1.7.7", "echarts": "^5.5.1", "echarts-gl": "^2.0.9", "element-plus": "^2.8.5", "lodash": "^4.17.21", "lottie-web": "^5.12.2", "mitt": "^3.0.1", "pinia": "^2.0.14", "pinia-plugin-persistedstate": "^4.1.2", "qs": "^6.13.0", "vue": "^3.2.37", "vue-router": "^4.1.2", "vue3-seamless-scroll": "^2.0.1"}, "devDependencies": {"@types/axios": "^0.9.36", "@types/node": "^16.18.119", "@types/qs": "^6.9.16", "@vitejs/plugin-vue": "^2.3.3", "@vitejs/plugin-vue-jsx": "^1.3.10", "@vue/tsconfig": "^0.1.3", "npm-run-all": "^4.1.5", "sass": "^1.80.3", "typescript": "~4.7.4", "unplugin-auto-import": "^0.18.3", "unplugin-vue-components": "^0.27.4", "vite": "^2.9.14", "vue-tsc": "^0.38.4"}}