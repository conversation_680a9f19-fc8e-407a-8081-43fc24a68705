{
  "extends": "@vue/tsconfig/tsconfig.web.json",
  "compilerOptions": {
    "target": "esnext",
    "module": "esnext",
    // "jsx": "preserve", // Specify JSX code generation: 'preserve', 'react-native', or 'react'.
    "moduleResolution": "node",
    // "isolatedModules": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "types": ["vite/client", "node"],
    // "allowImportingTsExtensions": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"]
    },
    
    "verbatimModuleSyntax": true, // Use this instead of deprecated options
    "ignoreDeprecations": "5.0" // Silence deprecation warnings
  },
  "include": ["src/**/*.ts", "src/**/*.vue", "src/**/*.tsx", "src/**/*.d.ts"], // **Represents any directory, and * represents any file. Indicates that all files in the src directory will be compiled
  "exclude": ["node_modules", "dist"] // Indicates the file directory that does not need to be compiled
}