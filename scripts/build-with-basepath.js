const fs = require('fs-extra');
const { execSync } = require('child_process');
const path = require('path');

// # 生产环境构建
// npm run build:custom /custom-path

// # 开发环境构建
// npm run build:custom /custom-path development

// 获取命令行参数
const basePath = process.argv[2];
const mode = process.argv[3] || 'production';

if (!basePath) {
  console.error('请提供 basePath 参数');
  console.log('使用方式: node scripts/build-with-basepath.js /your-path [mode]');
  process.exit(1);
}

// 确保 basePath 以 / 开头和结尾
const normalizedBasePath = basePath.startsWith('/') ? basePath : `/${basePath}`;
const finalBasePath = normalizedBasePath.endsWith('/') ? normalizedBasePath : `${normalizedBasePath}/`;

// 读取对应的 env 文件
const envFile = mode === 'development' ? '.env.development' : '.env.production';
const envPath = path.resolve(envFile);

try {
  // 备份原始文件
  const backupPath = `${envPath}.backup`;
  fs.copyFileSync(envPath, backupPath);

  // 读取文件内容
  let content = fs.readFileSync(envPath, 'utf8');

  // 替换 VITE_BASE_PATH 和 VITE_API_URL
  content = content.replace(
    /VITE_BASE_PATH\s*=\s*.*/,
    `VITE_BASE_PATH = '${finalBasePath}'`
  );
  content = content.replace(
    /VITE_API_URL\s*=\s*.*/,
    `VITE_API_URL = '${finalBasePath}api'`
  );

  // 写入修改后的内容
  fs.writeFileSync(envPath, content);

  console.log(`✅ 已更新 ${envFile}:`);
  console.log(`   VITE_BASE_PATH = '${finalBasePath}'`);
  console.log(`   VITE_API_URL = '${finalBasePath}api'`);

  // 执行构建
  const buildCommand = mode === 'development' ? 'npm run build:dev' : 'npm run build:pro';
  console.log(`🚀 开始执行: ${buildCommand}`);
  execSync(buildCommand, { stdio: 'inherit' });

  // 恢复原始文件
  fs.copyFileSync(backupPath, envPath);
  fs.removeSync(backupPath);
  console.log(`✅ 已恢复 ${envFile} 原始配置`);

} catch (error) {
  console.error('❌ 构建失败:', error.message);
  
  // 尝试恢复原始文件
  const backupPath = `${envPath}.backup`;
  if (fs.existsSync(backupPath)) {
    fs.copyFileSync(backupPath, envPath);
    fs.removeSync(backupPath);
    console.log(`✅ 已恢复 ${envFile} 原始配置`);
  }
  
  process.exit(1);
}