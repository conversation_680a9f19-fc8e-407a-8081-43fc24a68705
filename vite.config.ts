import { fileURLToPath, URL } from 'url'
import { loadEnv } from 'vite'
const root = process.cwd()

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
// import vueJsx from '@vitejs/plugin-vue-jsx'

import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  let env = {} as any
  // const isBuild = command === 'build'
  env = loadEnv(mode, root)
  return {
    base: env.VITE_BASE_PATH,
    plugins: [
      vue(),
      // vueJsx(),
      AutoImport({ resolvers: [ElementPlusResolver()], }),
      Components({ resolvers: [ElementPlusResolver()], }),
    ],
    server: {
      port: 4567,
      host: true,
      proxy: {
        // 选项写法
        '/api': {
          target: env.VITE_PROXY_TARGET,
          changeOrigin: true,
          rewrite: path => path.replace(/^\/api/, '')
        }
      },
      hmr: {
        overlay: false // 禁用错误覆盖层
      },
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    css: {
      // 全局配置 utils.scs，详细配置参考 vue-cli 官网
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/style/util.scss";`,
          quietDeps: true,// 静默依赖项的弃用警告
          silenceDeprecations: ['import', 'global-builtin', 'legacy-js-api']// 静默特定的弃用警告
        },
      } as Record<string, unknown>,
    },
  }
})
