# cache:
#   key: dashboard-cache
#   paths:
#     - dist/
stages:           
  - build
  - release

build-job:
  image: node:20.5.0-alpine         
  stage: build
  tags:
    - nodejs-dock
  before_script:
    # - npm config set registry https://registry.npmmirror.com
    - npm config set proxy http://***********:7890
    - npm config set https-proxy http://***********:7890
    - npm config set registry https://registry.npmjs.org/
    # - npm i --include=dev --cache .npm --prefer-offline
    - npm i --dev  --prefer-offline
  script:
    - npm run build:pro
    - ls $CI_PROJECT_DIR
    - echo "当前目录：" `pwd`
  artifacts:
    name: package
    paths:
      - dist

 
release-image:
  image: docker:26.1
  stage: release
  dependencies:
    - build-job
  tags:
    - nodejs-dock
  variables:
    CONTAINER_TEST_IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_SHORT_SHA
    CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:latest
  script:
    - ls -l .
    - source ./version
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker pull $CONTAINER_RELEASE_IMAGE || true
    - docker build --cache-from $CONTAINER_RELEASE_IMAGE -t $CONTAINER_TEST_IMAGE -t $CI_REGISTRY_IMAGE:$VERSION -t $CONTAINER_RELEASE_IMAGE .
    - docker push $CONTAINER_RELEASE_IMAGE
    - docker push $CI_REGISTRY_IMAGE:$VERSION
    - docker logout
    - docker rmi -f $CONTAINER_TEST_IMAGE $CI_REGISTRY_IMAGE:$VERSION $CONTAINER_RELEASE_IMAGE
    - docker images -f dangling=true -q | xargs --no-run-if-empty docker rmi
    - docker images
    - echo "clean up build files..."
    - rm -rf ./dist/

  only:
    - main
    - ci
    - backend
    - wb_dashboard2
  except:
    - tags

