/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Aside: typeof import('./src/components/layout/Aside.vue')['default']
    Bar: typeof import('./src/components/charts/Bar.vue')['default']
    Bar3D: typeof import('./src/components/charts/Bar3D.vue')['default']
    Center: typeof import('./src/components/layout/Center.vue')['default']
    ChartProto: typeof import('./src/components/charts/chartProto.vue')['default']
    DoubleBar: typeof import('./src/components/charts/DoubleBar.vue')['default']
    DoubleLine: typeof import('./src/components/charts/DoubleLine.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    Gauge: typeof import('./src/components/charts/Gauge.vue')['default']
    GaugeCircle: typeof import('./src/components/charts/GaugeCircle.vue')['default']
    LargeBorder: typeof import('./src/components/border/LargeBorder.vue')['default']
    Line: typeof import('./src/components/charts/Line.vue')['default']
    List: typeof import('./src/components/charts/List.vue')['default']
    MapDetail: typeof import('./src/components/MapDetail.vue')['default']
    NormalBorder: typeof import('./src/components/border/NormalBorder.vue')['default']
    PictorialBarCircle: typeof import('./src/components/charts/PictorialBarCircle.vue')['default']
    PictorialBarTriangle: typeof import('./src/components/charts/PictorialBarTriangle.vue')['default']
    Pie1: typeof import('./src/components/charts/Pie1.vue')['default']
    Pie2: typeof import('./src/components/charts/Pie2.vue')['default']
    Pie3D: typeof import('./src/components/charts/Pie3D.vue')['default']
    ProgressBar: typeof import('./src/components/charts/ProgressBar.vue')['default']
    ProgressList: typeof import('./src/components/charts/ProgressList.vue')['default']
    Pyramid: typeof import('./src/components/charts/Pyramid.vue')['default']
    Radar: typeof import('./src/components/charts/Radar.vue')['default']
    RingPie: typeof import('./src/components/charts/RingPie.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Scatter: typeof import('./src/components/charts/scatter.vue')['default']
    SingleBarWithLine: typeof import('./src/components/charts/SingleBarWithLine.vue')['default']
    StatisticCards: typeof import('./src/components/charts/StatisticCards.vue')['default']
    TooltipCard: typeof import('./src/components/charts/TooltipCard.vue')['default']
    TotalNum: typeof import('./src/components/TotalNum.vue')['default']
  }
}
